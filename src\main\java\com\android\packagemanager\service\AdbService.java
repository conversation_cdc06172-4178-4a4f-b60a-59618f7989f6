package com.android.packagemanager.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class AdbService {
    
    private static final Logger logger = LoggerFactory.getLogger(AdbService.class);
    
    @Value("${adb.path:adb}")
    private String adbPath;
    
    @Value("${adb.timeout:30000}")
    private long timeout;
    
    /**
     * 执行ADB命令
     */
    public AdbResult executeCommand(String... commands) {
        List<String> fullCommand = new ArrayList<>();
        fullCommand.add(adbPath);
        for (String cmd : commands) {
            fullCommand.add(cmd);
        }
        
        logger.debug("执行ADB命令: {}", String.join(" ", fullCommand));
        
        ProcessBuilder processBuilder = new ProcessBuilder(fullCommand);
        processBuilder.redirectErrorStream(true);
        
        long startTime = System.currentTimeMillis();
        
        try {
            Process process = processBuilder.start();
            
            List<String> output = new ArrayList<>();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.add(line);
                }
            }
            
            boolean finished = process.waitFor(timeout, TimeUnit.MILLISECONDS);
            long executionTime = System.currentTimeMillis() - startTime;
            
            if (!finished) {
                process.destroyForcibly();
                logger.error("ADB命令执行超时: {}", String.join(" ", fullCommand));
                return new AdbResult(false, output, "命令执行超时", executionTime);
            }
            
            int exitCode = process.exitValue();
            boolean success = exitCode == 0;
            
            if (!success) {
                logger.error("ADB命令执行失败，退出码: {}, 命令: {}", exitCode, String.join(" ", fullCommand));
            }
            
            return new AdbResult(success, output, success ? null : "命令执行失败，退出码: " + exitCode, executionTime);
            
        } catch (IOException | InterruptedException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logger.error("执行ADB命令时发生异常: {}", e.getMessage(), e);
            return new AdbResult(false, new ArrayList<>(), e.getMessage(), executionTime);
        }
    }
    
    /**
     * 获取系统应用包列表
     */
    public AdbResult getSystemPackages() {
        return executeCommand("shell", "pm", "list", "packages", "-s");
    }
    
    /**
     * 获取用户应用包列表
     */
    public AdbResult getUserPackages() {
        return executeCommand("shell", "pm", "list", "packages", "-3");
    }
    
    /**
     * 获取应用信息
     */
    public AdbResult getPackageInfo(String packageName) {
        return executeCommand("shell", "pm", "dump", packageName);
    }
    
    /**
     * 获取APK路径
     */
    public AdbResult getPackagePath(String packageName) {
        return executeCommand("shell", "pm", "path", packageName);
    }
    
    /**
     * 卸载系统应用
     */
    public AdbResult uninstallSystemApp(String packageName) {
        return executeCommand("shell", "pm", "uninstall", "--user", "0", packageName);
    }
    
    /**
     * 卸载用户应用
     */
    public AdbResult uninstallUserApp(String packageName) {
        return executeCommand("uninstall", packageName);
    }
    
    /**
     * 检查设备连接状态
     */
    public boolean isDeviceConnected() {
        AdbResult result = executeCommand("devices");
        if (!result.isSuccess()) {
            return false;
        }
        
        return result.getOutput().stream()
                .anyMatch(line -> line.contains("device") && !line.contains("List of devices"));
    }
    
    /**
     * ADB命令执行结果
     */
    public static class AdbResult {
        private final boolean success;
        private final List<String> output;
        private final String errorMessage;
        private final long executionTime;
        
        public AdbResult(boolean success, List<String> output, String errorMessage, long executionTime) {
            this.success = success;
            this.output = output;
            this.errorMessage = errorMessage;
            this.executionTime = executionTime;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public List<String> getOutput() {
            return output;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public long getExecutionTime() {
            return executionTime;
        }
        
        public String getOutputAsString() {
            return String.join("\n", output);
        }
    }
}
