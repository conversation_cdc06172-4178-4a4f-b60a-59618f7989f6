package com.android.packagemanager.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class AdbService {
    
    private static final Logger logger = LoggerFactory.getLogger(AdbService.class);
    
    @Value("${adb.path:adb}")
    private String adbPath;
    
    @Value("${adb.timeout:30000}")
    private long timeout;
    
    /**
     * 执行ADB命令
     */
    public AdbResult executeCommand(String... commands) {
        List<String> fullCommand = new ArrayList<>();
        fullCommand.add(adbPath);
        for (String cmd : commands) {
            fullCommand.add(cmd);
        }
        
        logger.debug("执行ADB命令: {}", String.join(" ", fullCommand));
        
        ProcessBuilder processBuilder = new ProcessBuilder(fullCommand);
        processBuilder.redirectErrorStream(true);
        
        long startTime = System.currentTimeMillis();
        
        try {
            Process process = processBuilder.start();
            
            List<String> output = new ArrayList<>();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.add(line);
                }
            }
            
            boolean finished = process.waitFor(timeout, TimeUnit.MILLISECONDS);
            long executionTime = System.currentTimeMillis() - startTime;
            
            if (!finished) {
                process.destroyForcibly();
                logger.error("ADB命令执行超时: {}", String.join(" ", fullCommand));
                return new AdbResult(false, output, "命令执行超时", executionTime);
            }
            
            int exitCode = process.exitValue();
            boolean success = exitCode == 0;
            
            if (!success) {
                logger.error("ADB命令执行失败，退出码: {}, 命令: {}", exitCode, String.join(" ", fullCommand));
            }
            
            return new AdbResult(success, output, success ? null : "命令执行失败，退出码: " + exitCode, executionTime);
            
        } catch (IOException | InterruptedException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logger.error("执行ADB命令时发生异常: {}", e.getMessage(), e);
            return new AdbResult(false, new ArrayList<>(), e.getMessage(), executionTime);
        }
    }
    
    /**
     * 获取系统应用包列表
     */
    public AdbResult getSystemPackages() {
        return executeCommand("shell", "pm", "list", "packages", "-s");
    }
    
    /**
     * 获取用户应用包列表
     */
    public AdbResult getUserPackages() {
        return executeCommand("shell", "pm", "list", "packages", "-3");
    }
    
    /**
     * 获取应用信息
     */
    public AdbResult getPackageInfo(String packageName) {
        return executeCommand("shell", "pm", "dump", packageName);
    }
    
    /**
     * 获取APK路径
     */
    public AdbResult getPackagePath(String packageName) {
        return executeCommand("shell", "pm", "path", packageName);
    }
    
    /**
     * 卸载系统应用
     */
    public AdbResult uninstallSystemApp(String packageName) {
        return executeCommand("shell", "pm", "uninstall", "--user", "0", packageName);
    }
    
    /**
     * 卸载用户应用
     */
    public AdbResult uninstallUserApp(String packageName) {
        return executeCommand("uninstall", packageName);
    }
    
    /**
     * 获取应用名称 - 参考PowerShell脚本逻辑
     */
    public String getAppName(String packageName) {
        try {
            // 方法1: 尝试使用ADB获取应用名称
            String appName = getAppNameFromDump(packageName);
            if (appName != null && !appName.isEmpty() && !"''".equals(appName)) {
                return appName;
            }

            // 方法2: 如果无法获取真实应用名称，尝试生成友好的显示名称
            String friendlyName = generateFriendlyName(packageName);
            if (friendlyName != null && !friendlyName.equals(packageName)) {
                logger.debug("为包 {} 生成友好名称: {}", packageName, friendlyName);
                return friendlyName;
            }

            // 如果都失败，返回包名
            return packageName;

        } catch (Exception e) {
            logger.error("获取应用名称异常: {}", packageName, e);
            return packageName;
        }
    }

    /**
     * 从包名生成友好的显示名称
     */
    private String generateFriendlyName(String packageName) {
        if (packageName == null || packageName.isEmpty()) {
            return packageName;
        }

        // 常见应用的映射
        switch (packageName) {
            case "com.android.chrome":
                return "Chrome浏览器";
            case "com.tencent.mm":
                return "微信";
            case "com.tencent.mobileqq":
                return "QQ";
            case "com.alibaba.android.rimet":
                return "钉钉";
            case "com.taobao.taobao":
                return "淘宝";
            case "com.tmall.wireless":
                return "天猫";
            case "com.jingdong.app.mall":
                return "京东";
            case "com.sina.weibo":
                return "微博";
            case "com.netease.cloudmusic":
                return "网易云音乐";
            case "com.tencent.qqmusic":
                return "QQ音乐";
            case "com.kugou.android":
                return "酷狗音乐";
            case "com.termux":
                return "Termux";
            case "com.miui.gallery":
                return "MIUI相册";
            case "com.miui.notes":
                return "MIUI便签";
            case "com.miui.cleanmaster":
                return "MIUI清理大师";
            case "com.miui.screenrecorder":
                return "MIUI录屏";
            case "li.songe.gkd":
                return "GKD";
            case "moe.shizuku.privileged.api":
                return "Shizuku";
            case "com.omarea.vtools":
                return "Scene";
        }

        // 尝试从包名中提取有意义的部分
        String[] parts = packageName.split("\\.");
        if (parts.length >= 2) {
            String lastPart = parts[parts.length - 1];
            String secondLastPart = parts.length > 2 ? parts[parts.length - 2] : "";

            // 如果最后一部分是有意义的词，使用它
            if (ismeaningfulWord(lastPart)) {
                return capitalizeFirst(lastPart);
            }

            // 如果倒数第二部分是有意义的词，使用它
            if (ismeaningfulWord(secondLastPart)) {
                return capitalizeFirst(secondLastPart);
            }
        }

        return packageName;
    }

    /**
     * 判断是否是有意义的词（不是常见的技术术语）
     */
    private boolean ismeaningfulWord(String word) {
        if (word == null || word.length() < 3) {
            return false;
        }

        // 排除常见的技术术语
        String[] techTerms = {"android", "app", "mobile", "client", "service", "api", "sdk", "lib", "core", "main", "activity"};
        for (String term : techTerms) {
            if (word.equalsIgnoreCase(term)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 首字母大写
     */
    private String capitalizeFirst(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }

    /**
     * 从pm dump输出中获取应用名称
     */
    private String getAppNameFromDump(String packageName) {
        try {
            // 直接获取完整dump信息然后解析（避免管道符号问题）
            AdbResult result = getPackageInfo(packageName);
            if (!result.isSuccess()) {
                return null;
            }

            // 在完整输出中查找applicationLabel
            for (String line : result.getOutput()) {
                if (line.contains("applicationLabel:")) {
                    String appName = parseApplicationLabel(line);
                    if (appName != null && !appName.isEmpty()) {
                        logger.debug("从dump获取到应用名称: {} -> {}", packageName, appName);
                        return appName;
                    }
                }
            }

            logger.debug("未在dump输出中找到applicationLabel: {}", packageName);
            return null;

        } catch (Exception e) {
            logger.debug("从dump获取应用名称失败: {}", packageName, e);
        }
        return null;
    }

    /**
     * 解析applicationLabel行
     */
    private String parseApplicationLabel(String line) {
        if (line == null || !line.contains("applicationLabel:")) {
            return null;
        }

        try {
            // 格式1: applicationLabel:'应用名称'
            if (line.contains("applicationLabel:'")) {
                int start = line.indexOf("applicationLabel:'") + 18;
                int end = line.indexOf("'", start);
                if (end > start) {
                    return line.substring(start, end).trim();
                }
            }

            // 格式2: applicationLabel:应用名称
            if (line.contains("applicationLabel:")) {
                String appName = line.substring(line.indexOf("applicationLabel:") + 17).trim();
                // 移除首尾的单引号
                appName = appName.replaceAll("^'|'$", "");
                // 移除换行符
                appName = appName.replaceAll("\\r|\\n", "");
                return appName.isEmpty() ? null : appName;
            }

        } catch (Exception e) {
            logger.debug("解析applicationLabel失败: {}", line, e);
        }

        return null;
    }

    /**
     * 检查设备连接状态
     */
    public boolean isDeviceConnected() {
        AdbResult result = executeCommand("devices");
        if (!result.isSuccess()) {
            return false;
        }

        return result.getOutput().stream()
                .anyMatch(line -> line.contains("device") && !line.contains("List of devices"));
    }
    
    /**
     * ADB命令执行结果
     */
    public static class AdbResult {
        private final boolean success;
        private final List<String> output;
        private final String errorMessage;
        private final long executionTime;
        
        public AdbResult(boolean success, List<String> output, String errorMessage, long executionTime) {
            this.success = success;
            this.output = output;
            this.errorMessage = errorMessage;
            this.executionTime = executionTime;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public List<String> getOutput() {
            return output;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public long getExecutionTime() {
            return executionTime;
        }
        
        public String getOutputAsString() {
            return String.join("\n", output);
        }
    }
}
