package com.android.packagemanager.repository;

import com.android.packagemanager.entity.AndroidPackage;
import com.android.packagemanager.entity.UninstallRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface UninstallRecordRepository extends JpaRepository<UninstallRecord, Long> {
    
    List<UninstallRecord> findByPackageName(String packageName);
    
    List<UninstallRecord> findByStatus(UninstallRecord.UninstallStatus status);
    
    List<UninstallRecord> findByPackageType(AndroidPackage.PackageType packageType);
    
    List<UninstallRecord> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    @Query("SELECT r FROM UninstallRecord r WHERE r.createdAt >= :startTime ORDER BY r.createdAt DESC")
    List<UninstallRecord> findRecentRecords(@Param("startTime") LocalDateTime startTime);
    
    @Query("SELECT COUNT(r) FROM UninstallRecord r WHERE r.status = :status")
    long countByStatus(@Param("status") UninstallRecord.UninstallStatus status);
    
    @Query("SELECT r FROM UninstallRecord r ORDER BY r.createdAt DESC")
    List<UninstallRecord> findAllOrderByCreatedAtDesc();
    
    @Query("SELECT r FROM UninstallRecord r WHERE r.packageType = :packageType AND r.status = :status")
    List<UninstallRecord> findByPackageTypeAndStatus(@Param("packageType") AndroidPackage.PackageType packageType,
                                                     @Param("status") UninstallRecord.UninstallStatus status);
}
