package com.android.packagemanager.service;

import com.android.packagemanager.entity.AndroidPackage;
import com.android.packagemanager.repository.AndroidPackageRepository;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Transactional
public class PackageService {
    
    private static final Logger logger = LoggerFactory.getLogger(PackageService.class);
    
    @Autowired
    private AndroidPackageRepository packageRepository;
    
    @Autowired
    private AdbService adbService;
    
    @Value("${file.system-app-list}")
    private String systemAppListFile;
    
    @Value("${file.user-app-list}")
    private String userAppListFile;
    
    /**
     * 从ADB实时获取系统应用列表
     */
    public List<AndroidPackage> getSystemPackagesFromAdb() {
        logger.info("从ADB获取系统应用列表");
        
        AdbService.AdbResult result = adbService.getSystemPackages();
        if (!result.isSuccess()) {
            logger.error("获取系统应用列表失败: {}", result.getErrorMessage());
            return new ArrayList<>();
        }
        
        List<AndroidPackage> packages = new ArrayList<>();
        Pattern packagePattern = Pattern.compile("package:(.+)");
        
        for (String line : result.getOutput()) {
            Matcher matcher = packagePattern.matcher(line.trim());
            if (matcher.matches()) {
                String packageName = matcher.group(1);
                AndroidPackage pkg = new AndroidPackage(packageName, null, AndroidPackage.PackageType.SYSTEM);
                
                // 尝试获取应用名称
                String appName = getAppNameFromAdb(packageName);
                pkg.setAppName(appName);
                
                packages.add(pkg);
            }
        }
        
        logger.info("获取到 {} 个系统应用", packages.size());
        return packages;
    }
    
    /**
     * 从ADB实时获取用户应用列表
     */
    public List<AndroidPackage> getUserPackagesFromAdb() {
        logger.info("从ADB获取用户应用列表");
        
        AdbService.AdbResult result = adbService.getUserPackages();
        if (!result.isSuccess()) {
            logger.error("获取用户应用列表失败: {}", result.getErrorMessage());
            return new ArrayList<>();
        }
        
        List<AndroidPackage> packages = new ArrayList<>();
        Pattern packagePattern = Pattern.compile("package:(.+)");
        
        for (String line : result.getOutput()) {
            Matcher matcher = packagePattern.matcher(line.trim());
            if (matcher.matches()) {
                String packageName = matcher.group(1);
                AndroidPackage pkg = new AndroidPackage(packageName, null, AndroidPackage.PackageType.USER);
                
                // 尝试获取应用名称
                String appName = getAppNameFromAdb(packageName);
                pkg.setAppName(appName);
                
                packages.add(pkg);
            }
        }
        
        logger.info("获取到 {} 个用户应用", packages.size());
        return packages;
    }
    
    /**
     * 从ADB获取应用名称 - 使用改进的逻辑
     */
    private String getAppNameFromAdb(String packageName) {
        // 使用AdbService中的新方法，该方法实现了PowerShell脚本的逻辑
        return adbService.getAppName(packageName);
    }
    
    /**
     * 从CSV文件加载系统应用
     */
    public List<AndroidPackage> loadSystemPackagesFromCsv() {
        return loadPackagesFromCsv(systemAppListFile, AndroidPackage.PackageType.SYSTEM);
    }
    
    /**
     * 从CSV文件加载用户应用
     */
    public List<AndroidPackage> loadUserPackagesFromCsv() {
        return loadPackagesFromCsv(userAppListFile, AndroidPackage.PackageType.USER);
    }
    
    /**
     * 从CSV文件加载应用包
     */
    private List<AndroidPackage> loadPackagesFromCsv(String csvFile, AndroidPackage.PackageType packageType) {
        List<AndroidPackage> packages = new ArrayList<>();
        
        try (CSVReader reader = new CSVReader(new FileReader(csvFile))) {
            List<String[]> records = reader.readAll();
            
            // 跳过标题行
            for (int i = 1; i < records.size(); i++) {
                String[] record = records.get(i);
                if (record.length >= 2) {
                    String packageName = record[0].trim();
                    String appName = record[1].trim();
                    
                    AndroidPackage pkg = new AndroidPackage(packageName, appName, packageType);
                    packages.add(pkg);
                }
            }
            
            logger.info("从CSV文件 {} 加载了 {} 个应用", csvFile, packages.size());
            
        } catch (IOException | CsvException e) {
            logger.error("读取CSV文件失败: {}", e.getMessage(), e);
        }
        
        return packages;
    }
    
    /**
     * 同步包信息到数据库
     */
    public void syncPackagesToDatabase() {
        logger.info("开始同步包信息到数据库");
        
        // 获取系统应用
        List<AndroidPackage> systemPackages = getSystemPackagesFromAdb();
        for (AndroidPackage pkg : systemPackages) {
            saveOrUpdatePackage(pkg);
        }
        
        // 获取用户应用
        List<AndroidPackage> userPackages = getUserPackagesFromAdb();
        for (AndroidPackage pkg : userPackages) {
            saveOrUpdatePackage(pkg);
        }
        
        logger.info("包信息同步完成");
    }
    
    /**
     * 保存或更新包信息
     */
    private void saveOrUpdatePackage(AndroidPackage pkg) {
        Optional<AndroidPackage> existing = packageRepository.findByPackageName(pkg.getPackageName());
        if (existing.isPresent()) {
            AndroidPackage existingPkg = existing.get();
            existingPkg.setAppName(pkg.getAppName());
            existingPkg.setPackageType(pkg.getPackageType());
            packageRepository.save(existingPkg);
        } else {
            packageRepository.save(pkg);
        }
    }
    
    /**
     * 获取所有系统应用
     */
    public List<AndroidPackage> getAllSystemPackages() {
        return packageRepository.findByPackageTypeOrderByAppName(AndroidPackage.PackageType.SYSTEM);
    }
    
    /**
     * 获取所有用户应用
     */
    public List<AndroidPackage> getAllUserPackages() {
        return packageRepository.findByPackageTypeOrderByAppName(AndroidPackage.PackageType.USER);
    }
    
    /**
     * 根据关键词搜索应用
     */
    public List<AndroidPackage> searchPackages(AndroidPackage.PackageType packageType, String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return packageRepository.findByPackageTypeOrderByAppName(packageType);
        }
        return packageRepository.findByPackageTypeAndKeyword(packageType, keyword.trim());
    }
    
    /**
     * 根据包名获取应用
     */
    public Optional<AndroidPackage> getPackageByName(String packageName) {
        return packageRepository.findByPackageName(packageName);
    }
    
    /**
     * 检查设备连接状态
     */
    public boolean isDeviceConnected() {
        return adbService.isDeviceConnected();
    }
}
