package com.android.packagemanager.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class WebController {
    
    /**
     * 主页
     */
    @GetMapping("/")
    public String index(Model model) {
        model.addAttribute("title", "Android 包管理系统");
        return "index";
    }
    
    /**
     * 包查询页面
     */
    @GetMapping("/packages")
    public String packages(Model model) {
        model.addAttribute("title", "包查询");
        return "packages";
    }
    
    /**
     * 包卸载页面
     */
    @GetMapping("/uninstall")
    public String uninstall(Model model) {
        model.addAttribute("title", "包卸载");
        return "uninstall";
    }
    
    /**
     * 卸载记录页面
     */
    @GetMapping("/records")
    public String records(Model model) {
        model.addAttribute("title", "卸载记录");
        return "records";
    }
    
    /**
     * 导出页面
     */
    @GetMapping("/export")
    public String export(Model model) {
        model.addAttribute("title", "数据导出");
        return "export";
    }
}
