# Android 包管理系统 - 项目说明

## 项目概述

本项目是一个基于Spring Boot的Android应用包管理系统，提供了完整的包查询、卸载和导出功能。系统通过ADB命令与Android设备通信，提供了友好的Web界面进行操作。

## 核心功能

### 1. 包查询功能
- **系统应用查询**：使用 `adb shell pm list packages -s` 获取系统应用
- **用户应用查询**：使用 `adb shell pm list packages -3` 获取用户应用
- **应用名称获取**：通过 `adb shell pm dump <package>` 获取应用的真实名称
- **实时查询**：支持从设备实时获取最新数据
- **关键词搜索**：支持按包名和应用名称搜索
- **分类显示**：清晰区分系统软件和用户软件

### 2. 包卸载功能
- **系统应用卸载**：使用命令 `adb shell pm uninstall --user 0 <package_name>`
- **用户应用卸载**：使用命令 `adb uninstall <package_name>`
- **单个卸载**：支持单个应用的精确卸载
- **批量卸载**：支持批量选择多个应用进行卸载
- **操作记录**：记录所有卸载操作的详细信息

### 3. 卸载记录管理
- **完整记录**：记录成功和失败的所有卸载操作
- **详细信息**：包括操作时间、应用信息、执行结果、错误信息、执行时间等
- **操作人员**：记录执行操作的人员信息
- **状态筛选**：支持按成功/失败状态筛选记录
- **时间筛选**：支持按时间范围查看记录
- **统计信息**：提供操作统计和成功率分析

### 4. 数据导出功能
- **系统应用导出**：导出系统应用列表到CSV文件
- **用户应用导出**：导出用户应用列表到CSV文件
- **全部应用导出**：导出所有应用的完整列表
- **卸载记录导出**：导出历史卸载记录
- **实时导出**：支持导出实时获取的最新数据
- **中文支持**：CSV文件支持中文，可在Excel中正常打开

## 技术架构

### 后端技术栈
- **Spring Boot 3.2.0**：主框架
- **Spring Data JPA**：数据访问层
- **SQLite**：轻量级数据库
- **Hibernate Community Dialects**：SQLite方言支持
- **OpenCSV**：CSV文件处理
- **Jackson**：JSON处理

### 前端技术栈
- **Thymeleaf**：服务端模板引擎
- **Bootstrap 5**：UI框架
- **jQuery**：JavaScript库
- **Font Awesome**：图标库

### 数据库设计
- **android_packages**：应用包信息表
- **uninstall_records**：卸载记录表

## 项目结构

```
android-package-manager/
├── src/main/java/com/android/packagemanager/
│   ├── controller/                 # 控制器层
│   │   ├── WebController.java      # Web页面控制器
│   │   ├── PackageController.java  # 包查询API
│   │   ├── UninstallController.java # 卸载API
│   │   └── ExportController.java   # 导出API
│   ├── service/                    # 服务层
│   │   ├── AdbService.java         # ADB命令服务
│   │   ├── PackageService.java     # 包管理服务
│   │   ├── UninstallService.java   # 卸载服务
│   │   └── ExportService.java      # 导出服务
│   ├── entity/                     # 实体类
│   │   ├── AndroidPackage.java     # 应用包实体
│   │   └── UninstallRecord.java    # 卸载记录实体
│   ├── repository/                 # 数据访问层
│   │   ├── AndroidPackageRepository.java
│   │   └── UninstallRecordRepository.java
│   └── AndroidPackageManagerApplication.java
├── src/main/resources/
│   ├── templates/                  # Thymeleaf模板
│   │   ├── layout.html            # 布局模板
│   │   ├── index.html             # 首页
│   │   ├── packages.html          # 包查询页面
│   │   ├── uninstall.html         # 包卸载页面
│   │   ├── records.html           # 卸载记录页面
│   │   └── export.html            # 数据导出页面
│   └── application.yml            # 配置文件
├── exports/                       # 导出文件目录
├── pom.xml                        # Maven配置
├── settings.xml                   # Maven仓库配置
├── start.bat                      # Windows启动脚本
├── start.sh                       # Linux/Mac启动脚本
└── README.md                      # 项目文档
```

## 使用说明

### 环境要求
1. **Java 17+**
2. **Maven 3.6+**
3. **ADB工具**：确保已安装并在PATH中
4. **Android设备**：通过USB连接并开启USB调试

### 启动步骤
1. 连接Android设备并开启USB调试
2. 运行启动脚本：
   - Windows: `start.bat`
   - Linux/Mac: `chmod +x start.sh && ./start.sh`
3. 访问 http://localhost:8080

### 功能使用
1. **设备连接检查**：导航栏显示设备连接状态
2. **包查询**：选择应用类型，可选实时获取，支持搜索
3. **包卸载**：输入包名列表，指定操作人员，执行批量卸载
4. **查看记录**：筛选查看历史卸载记录和统计信息
5. **数据导出**：选择导出类型，下载CSV文件

## API接口

### 包查询接口
- `GET /api/packages/system?realtime=false&keyword=` - 获取系统应用
- `GET /api/packages/user?realtime=false&keyword=` - 获取用户应用
- `GET /api/packages/all?realtime=false&keyword=` - 获取所有应用
- `GET /api/packages/{packageName}` - 获取应用详情
- `GET /api/packages/device/status` - 检查设备状态
- `POST /api/packages/sync` - 同步包信息到数据库

### 卸载接口
- `POST /api/uninstall/{packageName}?operator=admin` - 卸载单个应用
- `POST /api/uninstall/batch` - 批量卸载应用
- `GET /api/uninstall/records?status=&recentHours=24` - 获取卸载记录
- `GET /api/uninstall/statistics` - 获取卸载统计

### 导出接口
- `POST /api/export/system?realtime=false` - 导出系统应用
- `POST /api/export/user?realtime=false` - 导出用户应用
- `POST /api/export/all?realtime=false` - 导出所有应用
- `POST /api/export/uninstall-records` - 导出卸载记录
- `GET /api/export/download?file=filename.csv` - 下载导出文件

## 配置说明

### application.yml 主要配置
```yaml
server:
  port: 8080                    # 服务端口

spring:
  datasource:
    url: ******************************  # SQLite数据库文件

adb:
  path: adb                     # ADB命令路径
  timeout: 30000               # 命令超时时间(毫秒)

file:
  system-app-list: system_app_list.csv    # 系统应用CSV文件
  user-app-list: user_app_list.csv        # 用户应用CSV文件
  export-path: exports/                   # 导出文件目录
```

## 安全注意事项

1. **系统应用卸载风险**：卸载系统应用可能影响设备正常使用
2. **数据备份**：重要应用卸载前请做好数据备份
3. **网络安全**：系统无授权验证，请在安全环境中使用
4. **设备权限**：确保设备已授权USB调试

## 故障排除

### 常见问题
1. **设备未连接**：检查USB连接和调试授权
2. **ADB命令失败**：检查ADB路径和版本
3. **应用卸载失败**：某些系统应用受保护无法卸载
4. **编译失败**：检查Java和Maven版本，使用提供的settings.xml

### 日志查看
- 应用日志：控制台输出
- 数据库文件：package_manager.db
- 导出文件：exports/ 目录

## 扩展功能建议

1. **用户认证**：添加登录验证功能
2. **应用安装**：支持APK文件安装
3. **设备管理**：支持多设备管理
4. **定时任务**：定时同步应用信息
5. **权限管理**：细粒度的操作权限控制

## 技术支持

如有问题或建议，请查看README.md文档或提交Issue。
