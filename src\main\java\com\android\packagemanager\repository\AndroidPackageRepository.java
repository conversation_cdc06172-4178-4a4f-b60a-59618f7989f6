package com.android.packagemanager.repository;

import com.android.packagemanager.entity.AndroidPackage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AndroidPackageRepository extends JpaRepository<AndroidPackage, Long> {
    
    Optional<AndroidPackage> findByPackageName(String packageName);
    
    List<AndroidPackage> findByPackageType(AndroidPackage.PackageType packageType);
    
    List<AndroidPackage> findByPackageTypeOrderByAppName(AndroidPackage.PackageType packageType);
    
    @Query("SELECT p FROM AndroidPackage p WHERE p.appName LIKE %:appName%")
    List<AndroidPackage> findByAppNameContaining(@Param("appName") String appName);
    
    @Query("SELECT p FROM AndroidPackage p WHERE p.packageName LIKE %:packageName%")
    List<AndroidPackage> findByPackageNameContaining(@Param("packageName") String packageName);
    
    @Query("SELECT p FROM AndroidPackage p WHERE p.packageType = :packageType AND " +
           "(p.appName LIKE %:keyword% OR p.packageName LIKE %:keyword%)")
    List<AndroidPackage> findByPackageTypeAndKeyword(@Param("packageType") AndroidPackage.PackageType packageType,
                                                     @Param("keyword") String keyword);
    
    @Query("SELECT COUNT(p) FROM AndroidPackage p WHERE p.packageType = :packageType")
    long countByPackageType(@Param("packageType") AndroidPackage.PackageType packageType);
    
    boolean existsByPackageName(String packageName);
}
