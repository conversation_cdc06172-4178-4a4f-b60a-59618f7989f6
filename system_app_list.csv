﻿PackageName,AppName
"android","Android System"
"android.aosp.overlay","标签未找到"
"android.aosp.overlay.telephony","标签未找到"
"android.miui.home.launcher.res","标签未找到"
"android.miui.overlay","标签未找到"
"android.miui.overlay.telephony","标签未找到"
"android.overlay.common","标签未找到"
"android.overlay.target","标签未找到"
"android.qvaoverlay.common","标签未找到"
"com.android.adservices.api","Android System"
"com.android.apps.tag","Tags"
"com.android.backupconfirm","标签未找到"
"com.android.bips","System printing service"
"com.android.bluetooth","Bluetooth"
"com.android.bluetooth.overlay","标签未找到"
"com.android.bluetoothmidiservice","Bluetooth MIDI Service"
"com.android.browser","Browser"
"com.android.calllogbackup","Call Log Backup/Restore"
"com.android.camera","Camera"
"com.android.cameraextensions","CameraExtensionsProxy"
"com.android.captiveportallogin","Captive Portal Login"
"com.android.carrierconfig","标签未找到"
"com.android.carrierconfig.overlay.common","标签未找到"
"com.android.carrierconfig.overlay.miui","标签未找到"
"com.android.carrierdefaultapp","Carrier Communications"
"com.android.cellbroadcastreceiver","标签未找到"
"com.android.cellbroadcastreceiver.overlay.common","标签未找到"
"com.android.cellbroadcastservice","Cell Broadcast Service"
"com.android.certinstaller","Certificate Installer"
"com.android.companiondevicemanager","Companion Device Manager"
"com.android.connectivity.resources","System Connectivity Resources"
"com.android.contacts","Contacts and dialer"
"com.android.credentialmanager","Credential Manager"
"com.android.cts.ctsshim","标签未找到"
"com.android.cts.priv.ctsshim","标签未找到"
"com.android.devicelockcontroller","DeviceLockController"
"com.android.dreams.basic","Basic Daydreams"
"com.android.dynsystem","Dynamic System Updates"
"com.android.emergency","Emergency information"
"com.android.externalstorage","External Storage"
"com.android.federatedcompute.services","标签未找到"
"com.android.fileexplorer","File Manager"
"com.android.health.connect.backuprestore","标签未找到"
"com.android.healthconnect.controller","Health Connect"
"com.android.hotspot2.osulogin","OsuLogin"
"com.android.htmlviewer","HTML Viewer"
"com.android.incallui","Phone"
"com.android.inputdevices","Input Devices"
"com.android.inputsettings.overlay.miui","标签未找到"
"com.android.intentresolver","IntentResolver"
"com.android.internal.display.cutout.emulation.corner","Corner cutout"
"com.android.internal.display.cutout.emulation.double","Double cutout"
"com.android.internal.display.cutout.emulation.hole","Punch Hole cutout"
"com.android.internal.display.cutout.emulation.tall","Tall cutout"
"com.android.internal.display.cutout.emulation.waterfall","Waterfall cutout"
"com.android.internal.systemui.navbar.gestural","Gestural Navigation Bar"
"com.android.internal.systemui.navbar.gestural_extra_wide_back","Gestural Navigation Bar"
"com.android.internal.systemui.navbar.gestural_narrow_back","Gestural Navigation Bar"
"com.android.internal.systemui.navbar.gestural_wide_back","Gestural Navigation Bar"
"com.android.internal.systemui.navbar.threebutton","3 Button Navigation Bar"
"com.android.internal.systemui.navbar.transparent","Transparent navigation bar"
"com.android.keychain","Key Chain"
"com.android.localtransport","标签未找到"
"com.android.location.fused","Fused Location"
"com.android.managedprovisioning","Work Setup"
"com.android.managedprovisioning.overlay","标签未找到"
"com.android.mms","Messaging"
"com.android.mms.service","MmsService"
"com.android.modulemetadata","Module Metadata"
"com.android.mtp","MTP Host"
"com.android.musicfx","MusicFX"
"com.android.nearby.halfsheet","标签未找到"
"com.android.networkstack","Network manager"
"com.android.networkstack.overlay.miui","NetworkStackOverlay"
"com.android.networkstack.tethering","Tethering"
"com.android.nfc","NFC Service"
"com.android.ondevicepersonalization.services","标签未找到"
"com.android.ons","标签未找到"
"com.android.overlay.cngmstelecomm","标签未找到"
"com.android.overlay.gmscontactprovider","标签未找到"
"com.android.overlay.gmssettingprovider","标签未找到"
"com.android.overlay.gmssettings","标签未找到"
"com.android.overlay.gmstelecomm","标签未找到"
"com.android.overlay.gmstelephony","标签未找到"
"com.android.overlay.systemui","标签未找到"
"com.android.pacprocessor","PacProcessor"
"com.android.permissioncontroller","Permission controller"
"com.android.phone","Phone Services"
"com.android.phone.common.overlay.miui","标签未找到"
"com.android.phone.overlay.common","标签未找到"
"com.android.phone.overlay.miui","标签未找到"
"com.android.printspooler","Print Spooler"
"com.android.providers.blockednumber","Blocked Numbers Storage"
"com.android.providers.calendar","Calendar storage"
"com.android.providers.contacts","Contacts Storage"
"com.android.providers.downloads","Downloads"
"com.android.providers.media","标签未找到"
"com.android.providers.media.module","Media"
"com.android.providers.partnerbookmarks","标签未找到"
"com.android.providers.settings","Settings Storage"
"com.android.providers.settings.overlay","标签未找到"
"com.android.providers.telephony","Phone and Messaging Storage"
"com.android.providers.telephony.overlay.miui","标签未找到"
"com.android.providers.userdictionary","User Dictionary"
"com.android.provision","Provision"
"com.android.provision.resource.overlay","标签未找到"
"com.android.proxyhandler","ProxyHandler"
"com.android.quicksearchbox","Search"
"com.android.rkpdapp","RemoteProvisioner"
"com.android.role.notes.enabled","Notes Role enabled"
"com.android.safetycenter.resources","Safety Center Resources"
"com.android.sdksandbox","标签未找到"
"com.android.se","SecureElementApplication"
"com.android.se.overlay.target","标签未找到"
"com.android.server.telecom","Phone Calls"
"com.android.server.telecom.overlay.common","标签未找到"
"com.android.server.telecom.overlay.miui","Manage calls"
"com.android.settings","Settings"
"com.android.settings.intelligence","Settings Suggestions"
"com.android.settings.overlay.common","标签未找到"
"com.android.settings.overlay.miui","标签未找到"
"com.android.settings.resource.overlay","标签未找到"
"com.android.sharedstoragebackup","标签未找到"
"com.android.shell","Shell"
"com.android.simappdialog","Sim App Dialog"
"com.android.smspush","标签未找到"
"com.android.statementservice","Intent Filter Verification Service"
"com.android.stk","SIM Toolkit"
"com.android.stk.overlay.miui","标签未找到"
"com.android.storagemanager","Storage Manager"
"com.android.systemui","System UI"
"com.android.systemui.accessibility.accessibilitymenu","标签未找到"
"com.android.systemui.gesture.line.overlay","标签未找到"
"com.android.systemui.navigation.bar.overlay","标签未找到"
"com.android.systemui.overlay.common","标签未找到"
"com.android.systemui.overlay.miui","标签未找到"
"com.android.theme.font.notoserifsource","Noto Serif / Source Sans Pro"
"com.android.thememanager","Themes"
"com.android.thememanager.customthemeconfig.config.overlay","标签未找到"
"com.android.traceur","System Tracing"
"com.android.updater","Updater"
"com.android.uwb.resources","System UWB Resources"
"com.android.vending","Google Play Services Updater"
"com.android.virtualmachine.res","标签未找到"
"com.android.vpndialogs","VpnDialogs"
"com.android.wallpaper.livepicker","Live Wallpaper Picker"
"com.android.wallpaperbackup","标签未找到"
"com.android.wallpapercropper","标签未找到"
"com.android.wifi.dialog","标签未找到"
"com.android.wifi.resources","System WLAN Resources"
"com.android.wifi.resources.overlay.common","标签未找到"
"com.android.wifi.resources.overlay.kalama","标签未找到"
"com.android.wifi.resources.overlay.target","标签未找到"
"com.android.wifi.resources.xiaomi","标签未找到"
"com.bsp.catchlog","Log generator"
"com.fido.asm","FIDO UAF1.0 ASM"
"com.goodix.fingerprint.setting","标签未找到"
"com.google.android.accessibility.switchaccess","Switch Access"
"com.google.android.cellbroadcastreceiver.overlay.miui","标签未找到"
"com.google.android.cellbroadcastservice.overlay.miui","标签未找到"
"com.google.android.configupdater","ConfigUpdater"
"com.google.android.documentsui","处理错误: 不能对 Null 值表达式调用方法。"
"com.google.android.ext.services","处理错误: 不能对 Null 值表达式调用方法。"
"com.google.android.ext.shared","Android Shared Library"
"com.google.android.gms","Google Play services"
"com.google.android.gms.location.history","Google Location History"
"com.google.android.gsf","Google Services Framework"
"com.google.android.marvin.talkback","Android Accessibility Suite"
"com.google.android.onetimeinitializer","Google One Time Init"
"com.google.android.overlay.gmsconfig","标签未找到"
"com.google.android.overlay.modules.ext.services.cn","标签未找到"
"com.google.android.printservice.recommendation","Print Service Recommendation Service"
"com.google.android.webview","Android System WebView"
"com.google.android.wifi.resources.overlay.common","标签未找到"
"com.goolge.android.wifi.resources.xiaomi","标签未找到"
"com.lbe.security.miui","Permissions"
"com.milink.service","Interconnectivity services"
"com.miui.accessibility","Mi Accessibility"
"com.miui.analytics","Analytics"
"com.miui.aod","Always-on display and Lock screen editor"
"com.miui.audiomonitor","Recording assistant"
"com.miui.backup","Backup"
"com.miui.bugreport","Feedback"
"com.miui.carlink","CarWith"
"com.miui.cit","CIT"
"com.miui.cloudbackup","Cloud backup"
"com.miui.cloudservice","Xiaomi Cloud"
"com.miui.contentcatcher","Application Extension Service"
"com.miui.contentextension","Taplus"
"com.miui.core","System SDK"
"com.miui.core.internal.services","标签未找到"
"com.miui.daemon","System Daemon"
"com.miui.dmregservice","RegService"
"com.miui.extraphoto","Bokeh"
"com.miui.freeform","Floating windows"
"com.miui.greenguard","Family Guard"
"com.miui.guardprovider","System security components"
"com.miui.home","System launcher"
"com.miui.hybrid","Quick apps Service Framework"
"com.miui.mediaviewer","Media viewer"
"com.miui.micloudsync","MiCloudSync"
"com.miui.mishare.connectivity","Mi Share"
"com.miui.misightservice","System quality monitoring"
"com.miui.misound","Earphones"
"com.miui.miwallpaper","MiWallpaper"
"com.miui.miwallpaper.config.overlay","标签未找到"
"com.miui.miwallpaper.overlay.customize","标签未找到"
"com.miui.nextpay","Smart cards Web Extention"
"com.miui.notification","Notifications"
"com.miui.otaprovision","OtaProvision"
"com.miui.packageinstaller","Package installer"
"com.miui.personalassistant","App vault"
"com.miui.phrase","Frequent phrases"
"com.miui.powerkeeper","Battery and performance"
"com.miui.qr","标签未找到"
"com.miui.rom","标签未找到"
"com.miui.screenshot","Screenshot"
"com.miui.securityadd","System service plugin"
"com.miui.securitycenter","Security"
"com.miui.securitycenter.securitycenter_phone_overlay.config.overlay","标签未找到"
"com.miui.securitycore","Security Core Component"
"com.miui.securityinputmethod","Mi Secure Keyboard"
"com.miui.settings.rro.device.config.overlay","标签未找到"
"com.miui.settings.rro.device.hide.statusbar.overlay","标签未找到"
"com.miui.settings.rro.device.systemui.overlay","标签未找到"
"com.miui.settings.rro.device.type.overlay","标签未找到"
"com.miui.system","标签未找到"
"com.miui.system.overlay","标签未找到"
"com.miui.systemAdSolution","智能服务"
"com.miui.systemui.carriers.overlay","MccMncOverlay"
"com.miui.systemui.devices.overlay","DevicesOverlay"
"com.miui.systemui.overlay.devices.android","DevicesAndroidOverlay"
"com.miui.touchassistant","Quick ball"
"com.miui.tsmclient","Smart cards"
"com.miui.uireporter","UIReporter"
"com.miui.voiceassist","Mi AI"
"com.miui.voiceassistoverlay","标签未找到"
"com.miui.voicetrigger","Wake with voice"
"com.miui.vsimcore","MConnService"
"com.miui.wallpaper.overlay.customize","标签未找到"
"com.miui.wmsvc","WMService"
"com.miui.yellowpage","Yellow pages"
"com.miuix.editor","textaction"
"com.mobiletools.systemhelper","SystemHelper"
"com.qti.dcf","dcf"
"com.qti.dpmserviceapp","标签未找到"
"com.qti.phone","标签未找到"
"com.qti.qcc","QCC"
"com.qti.qualcomm.datastatusnotification","标签未找到"
"com.qti.qualcomm.deviceinfo","Device Info"
"com.qti.service.colorservice","标签未找到"
"com.qualcomm.atfwd","标签未找到"
"com.qualcomm.atfwd2","标签未找到"
"com.qualcomm.location","LocationServices"
"com.qualcomm.qcrilmsgtunnel","标签未找到"
"com.qualcomm.qti.biometrics.fingerprint.service","标签未找到"
"com.qualcomm.qti.cne","标签未找到"
"com.qualcomm.qti.confdialer","ConfDialer"
"com.qualcomm.qti.devicestatisticsservice","标签未找到"
"com.qualcomm.qti.dynamicddsservice","标签未找到"
"com.qualcomm.qti.gpudrivers.kalama.api33","标签未找到"
"com.qualcomm.qti.lpa","标签未找到"
"com.qualcomm.qti.performancemode","Performance Mode"
"com.qualcomm.qti.poweroffalarm","PowerOffAlarm"
"com.qualcomm.qti.powersavemode","Usable Power Mode"
"com.qualcomm.qti.qcolor","QColor"
"com.qualcomm.qti.qms.service.trustzoneaccess","标签未找到"
"com.qualcomm.qti.remoteSimlockAuth","标签未找到"
"com.qualcomm.qti.ridemodeaudio","RideMode Recording list"
"com.qualcomm.qti.server.qtiwifi","QtiWifiService"
"com.qualcomm.qti.services.systemhelper","System Helper Service"
"com.qualcomm.qti.simcontacts","SimContacts"
"com.qualcomm.qti.telephonyservice","标签未找到"
"com.qualcomm.qti.trustedui","标签未找到"
"com.qualcomm.qti.uceShimService","uceShimService"
"com.qualcomm.qti.uim","标签未找到"
"com.qualcomm.qti.uimGbaApp","标签未找到"
"com.qualcomm.qti.workloadclassifier","标签未找到"
"com.qualcomm.qti.xrcb","XRCB"
"com.qualcomm.qti.xrvd.service","XRVD"
"com.qualcomm.qtil.btdsda","标签未找到"
"com.qualcomm.timeservice","标签未找到"
"com.qualcomm.uimremoteclient","标签未找到"
"com.qualcomm.uimremoteserver","标签未找到"
"com.qualcomm.wfd.service","Wfd Service"
"com.quicinc.voice.activation","Qualcomm Voice Assist"
"com.rongcard.eid","标签未找到"
"com.sohu.inputmethod.sogou.xiaomi","搜狗输入法小米版"
"com.tencent.soter.soterserver","SoterService"
"com.unionpay.tsmservice.mi","银联可信服务安全组件小米版本"
"com.wapi.wapicertmanage","WAPI certificate"
"com.xiaomi.ab","Mi Store System Components"
"com.xiaomi.account","Xiaomi Account"
"com.xiaomi.aiasst.service","Mi AI Call Assistant"
"com.xiaomi.aiasst.vision","Mi AI Translate"
"com.xiaomi.aicr","Mi AI Engine"
"com.xiaomi.aireco","小爱建议"
"com.xiaomi.barrage","Bullet screen notification"
"com.xiaomi.bluetooth","Bluetooth Extension"
"com.xiaomi.bluetooth.rro.device.config.overlay","标签未找到"
"com.xiaomi.cameratools","CameraTools"
"com.xiaomi.digitalkey","digitalkey"
"com.xiaomi.finddevice","Find device"
"com.xiaomi.gamecenter.sdk.service","游戏服务"
"com.xiaomi.gnss.polaris","Polaris"
"com.xiaomi.joyose","Joyose"
"com.xiaomi.location.fused","Fused location provider"
"com.xiaomi.macro","MiMacro"
"com.xiaomi.market","GetApps"
"com.xiaomi.metoknlp","Network location provider"
"com.xiaomi.mi_connect_service","Mi Connect"
"com.xiaomi.micloud.sdk","标签未找到"
"com.xiaomi.migameservice","Mi Game Service"
"com.xiaomi.mirror","Device interconnectivity services"
"com.xiaomi.mis","小米汽车互联服务"
"com.xiaomi.misettings","Settings"
"com.xiaomi.mtb","Rueban(MTB)V6.5.20231026"
"com.xiaomi.otrpbroker","otrpbroker"
"com.xiaomi.payment","Mi Coin"
"com.xiaomi.registration","Carrier Services"
"com.xiaomi.security.onetrack","SecurityOnetrackService"
"com.xiaomi.simactivate.service","Xiaomi SIM Activation Service"
"com.xiaomi.touchservice","标签未找到"
"com.xiaomi.trustservice","MiTrustService"
"com.xiaomi.ugd","GPU Driver Updater"
"com.xiaomi.xaee","XiaoaiEdgeEngine"
"com.xiaomi.xmsf","Xiaomi service framework"
"com.xiaomi.xmsfkeeper","Xiaomi Service Framework Keeper"
"miui.systemui.plugin","System UI Plug-in"
"org.codeaurora.ims","标签未找到"
"org.ifaa.aidl.manager","IfaaService"
"org.mipay.android.manager","MipayService"
"vendor.qti.hardware.cacert.server","标签未找到"
"vendor.qti.imsdatachannel","标签未找到"
"vendor.qti.imsrcs","标签未找到"
"vendor.qti.iwlan","标签未找到"
"vendor.qti.qesdk.sysservice","QesdkSysApp"
