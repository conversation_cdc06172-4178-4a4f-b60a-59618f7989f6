package com.android.packagemanager.service;

import com.android.packagemanager.entity.AndroidPackage;
import com.android.packagemanager.entity.UninstallRecord;
import com.android.packagemanager.repository.AndroidPackageRepository;
import com.android.packagemanager.repository.UninstallRecordRepository;
import com.opencsv.CSVWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class ExportService {
    
    private static final Logger logger = LoggerFactory.getLogger(ExportService.class);
    
    @Autowired
    private AndroidPackageRepository packageRepository;
    
    @Autowired
    private UninstallRecordRepository uninstallRecordRepository;
    
    @Autowired
    private PackageService packageService;
    
    @Value("${file.export-path}")
    private String exportPath;
    
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
    
    /**
     * 导出系统应用列表到CSV
     */
    public ExportResult exportSystemPackages(boolean realtime) {
        logger.info("导出系统应用列表，实时: {}", realtime);
        
        try {
            List<AndroidPackage> packages;
            if (realtime) {
                packages = packageService.getSystemPackagesFromAdb();
            } else {
                packages = packageRepository.findByPackageTypeOrderByAppName(AndroidPackage.PackageType.SYSTEM);
            }
            
            String fileName = String.format("system_packages_%s.csv", 
                    LocalDateTime.now().format(DATETIME_FORMATTER));
            String filePath = ensureExportDirectory() + File.separator + fileName;
            
            writePackagesToCsv(packages, filePath);
            
            logger.info("系统应用列表导出完成: {}, 共 {} 个应用", filePath, packages.size());
            return new ExportResult(true, filePath, packages.size(), "系统应用列表导出成功");
            
        } catch (Exception e) {
            logger.error("导出系统应用列表失败", e);
            return new ExportResult(false, null, 0, "导出失败: " + e.getMessage());
        }
    }
    
    /**
     * 导出用户应用列表到CSV
     */
    public ExportResult exportUserPackages(boolean realtime) {
        logger.info("导出用户应用列表，实时: {}", realtime);
        
        try {
            List<AndroidPackage> packages;
            if (realtime) {
                packages = packageService.getUserPackagesFromAdb();
            } else {
                packages = packageRepository.findByPackageTypeOrderByAppName(AndroidPackage.PackageType.USER);
            }
            
            String fileName = String.format("user_packages_%s.csv", 
                    LocalDateTime.now().format(DATETIME_FORMATTER));
            String filePath = ensureExportDirectory() + File.separator + fileName;
            
            writePackagesToCsv(packages, filePath);
            
            logger.info("用户应用列表导出完成: {}, 共 {} 个应用", filePath, packages.size());
            return new ExportResult(true, filePath, packages.size(), "用户应用列表导出成功");
            
        } catch (Exception e) {
            logger.error("导出用户应用列表失败", e);
            return new ExportResult(false, null, 0, "导出失败: " + e.getMessage());
        }
    }
    
    /**
     * 导出所有应用列表到CSV
     */
    public ExportResult exportAllPackages(boolean realtime) {
        logger.info("导出所有应用列表，实时: {}", realtime);
        
        try {
            List<AndroidPackage> systemPackages;
            List<AndroidPackage> userPackages;
            
            if (realtime) {
                systemPackages = packageService.getSystemPackagesFromAdb();
                userPackages = packageService.getUserPackagesFromAdb();
            } else {
                systemPackages = packageRepository.findByPackageTypeOrderByAppName(AndroidPackage.PackageType.SYSTEM);
                userPackages = packageRepository.findByPackageTypeOrderByAppName(AndroidPackage.PackageType.USER);
            }
            
            String fileName = String.format("all_packages_%s.csv", 
                    LocalDateTime.now().format(DATETIME_FORMATTER));
            String filePath = ensureExportDirectory() + File.separator + fileName;
            
            writeAllPackagesToCsv(systemPackages, userPackages, filePath);
            
            int totalCount = systemPackages.size() + userPackages.size();
            logger.info("所有应用列表导出完成: {}, 系统应用: {}, 用户应用: {}, 总计: {}", 
                       filePath, systemPackages.size(), userPackages.size(), totalCount);
            
            return new ExportResult(true, filePath, totalCount, 
                    String.format("所有应用列表导出成功，系统应用: %d, 用户应用: %d", 
                                 systemPackages.size(), userPackages.size()));
            
        } catch (Exception e) {
            logger.error("导出所有应用列表失败", e);
            return new ExportResult(false, null, 0, "导出失败: " + e.getMessage());
        }
    }
    
    /**
     * 导出卸载记录到CSV
     */
    public ExportResult exportUninstallRecords() {
        logger.info("导出卸载记录");
        
        try {
            List<UninstallRecord> records = uninstallRecordRepository.findAllOrderByCreatedAtDesc();
            
            String fileName = String.format("uninstall_records_%s.csv", 
                    LocalDateTime.now().format(DATETIME_FORMATTER));
            String filePath = ensureExportDirectory() + File.separator + fileName;
            
            writeUninstallRecordsToCsv(records, filePath);
            
            logger.info("卸载记录导出完成: {}, 共 {} 条记录", filePath, records.size());
            return new ExportResult(true, filePath, records.size(), "卸载记录导出成功");
            
        } catch (Exception e) {
            logger.error("导出卸载记录失败", e);
            return new ExportResult(false, null, 0, "导出失败: " + e.getMessage());
        }
    }
    
    /**
     * 写入应用包信息到CSV文件
     */
    private void writePackagesToCsv(List<AndroidPackage> packages, String filePath) throws IOException {
        // 写入BOM以支持Excel正确显示中文
        try (FileOutputStream fos = new FileOutputStream(filePath);
             OutputStreamWriter osw = new OutputStreamWriter(fos, "UTF-8")) {
            osw.write('\ufeff');
        }

        // 重新打开文件追加内容
        try (FileOutputStream fos = new FileOutputStream(filePath, true);
             OutputStreamWriter osw = new OutputStreamWriter(fos, "UTF-8");
             CSVWriter csvWriter = new CSVWriter(osw)) {
                // 写入标题行
                String[] header = {"包名", "应用名称", "应用类型", "版本名称", "版本代码", "是否启用", "APK路径", "创建时间", "更新时间"};
                csvWriter.writeNext(header);
                
                // 写入数据行
                for (AndroidPackage pkg : packages) {
                    String[] row = {
                        pkg.getPackageName() != null ? pkg.getPackageName() : "",
                        pkg.getAppName() != null ? pkg.getAppName() : "",
                        pkg.getPackageType() != null ? pkg.getPackageType().toString() : "",
                        pkg.getVersionName() != null ? pkg.getVersionName() : "",
                        pkg.getVersionCode() != null ? pkg.getVersionCode() : "",
                        pkg.getIsEnabled() != null ? pkg.getIsEnabled().toString() : "",
                        pkg.getApkPath() != null ? pkg.getApkPath() : "",
                        pkg.getCreatedAt() != null ? pkg.getCreatedAt().toString() : "",
                        pkg.getUpdatedAt() != null ? pkg.getUpdatedAt().toString() : ""
                    };
                    csvWriter.writeNext(row);
                }
            }
    }
    
    /**
     * 写入所有应用包信息到CSV文件（分类显示）
     */
    private void writeAllPackagesToCsv(List<AndroidPackage> systemPackages,
                                      List<AndroidPackage> userPackages, String filePath) throws IOException {
        // 写入BOM
        try (FileOutputStream fos = new FileOutputStream(filePath);
             OutputStreamWriter osw = new OutputStreamWriter(fos, "UTF-8")) {
            osw.write('\ufeff');
        }

        try (FileOutputStream fos = new FileOutputStream(filePath, true);
             OutputStreamWriter osw = new OutputStreamWriter(fos, "UTF-8");
             CSVWriter csvWriter = new CSVWriter(osw)) {
                // 写入标题行
                String[] header = {"包名", "应用名称", "应用类型", "版本名称", "版本代码", "是否启用", "APK路径", "创建时间", "更新时间"};
                csvWriter.writeNext(header);
                
                // 写入系统应用分类标题
                csvWriter.writeNext(new String[]{"=== 系统应用 ==="});
                for (AndroidPackage pkg : systemPackages) {
                    String[] row = {
                        pkg.getPackageName() != null ? pkg.getPackageName() : "",
                        pkg.getAppName() != null ? pkg.getAppName() : "",
                        "SYSTEM",
                        pkg.getVersionName() != null ? pkg.getVersionName() : "",
                        pkg.getVersionCode() != null ? pkg.getVersionCode() : "",
                        pkg.getIsEnabled() != null ? pkg.getIsEnabled().toString() : "",
                        pkg.getApkPath() != null ? pkg.getApkPath() : "",
                        pkg.getCreatedAt() != null ? pkg.getCreatedAt().toString() : "",
                        pkg.getUpdatedAt() != null ? pkg.getUpdatedAt().toString() : ""
                    };
                    csvWriter.writeNext(row);
                }
                
                // 写入用户应用分类标题
                csvWriter.writeNext(new String[]{"=== 用户应用 ==="});
                for (AndroidPackage pkg : userPackages) {
                    String[] row = {
                        pkg.getPackageName() != null ? pkg.getPackageName() : "",
                        pkg.getAppName() != null ? pkg.getAppName() : "",
                        "USER",
                        pkg.getVersionName() != null ? pkg.getVersionName() : "",
                        pkg.getVersionCode() != null ? pkg.getVersionCode() : "",
                        pkg.getIsEnabled() != null ? pkg.getIsEnabled().toString() : "",
                        pkg.getApkPath() != null ? pkg.getApkPath() : "",
                        pkg.getCreatedAt() != null ? pkg.getCreatedAt().toString() : "",
                        pkg.getUpdatedAt() != null ? pkg.getUpdatedAt().toString() : ""
                    };
                    csvWriter.writeNext(row);
                }
            }
    }
    
    /**
     * 写入卸载记录到CSV文件
     */
    private void writeUninstallRecordsToCsv(List<UninstallRecord> records, String filePath) throws IOException {
        // 写入BOM
        try (FileOutputStream fos = new FileOutputStream(filePath);
             OutputStreamWriter osw = new OutputStreamWriter(fos, "UTF-8")) {
            osw.write('\ufeff');
        }

        try (FileOutputStream fos = new FileOutputStream(filePath, true);
             OutputStreamWriter osw = new OutputStreamWriter(fos, "UTF-8");
             CSVWriter csvWriter = new CSVWriter(osw)) {
            // 写入标题行
            String[] header = {"包名", "应用名称", "应用类型", "卸载状态", "使用命令", "错误信息", "执行时间(ms)", "操作人", "操作时间"};
            csvWriter.writeNext(header);
            
            // 写入数据行
            for (UninstallRecord record : records) {
                String[] row = {
                    record.getPackageName() != null ? record.getPackageName() : "",
                    record.getAppName() != null ? record.getAppName() : "",
                    record.getPackageType() != null ? record.getPackageType().toString() : "",
                    record.getStatus() != null ? record.getStatus().toString() : "",
                    record.getCommandUsed() != null ? record.getCommandUsed() : "",
                    record.getErrorMessage() != null ? record.getErrorMessage() : "",
                    record.getExecutionTime() != null ? record.getExecutionTime().toString() : "",
                    record.getOperator() != null ? record.getOperator() : "",
                    record.getCreatedAt() != null ? record.getCreatedAt().toString() : ""
                };
                csvWriter.writeNext(row);
            }
        }
    }
    
    /**
     * 确保导出目录存在
     */
    private String ensureExportDirectory() {
        File exportDir = new File(exportPath);
        if (!exportDir.exists()) {
            exportDir.mkdirs();
        }
        return exportDir.getAbsolutePath();
    }
    
    /**
     * 导出结果
     */
    public static class ExportResult {
        private final boolean success;
        private final String filePath;
        private final int recordCount;
        private final String message;
        
        public ExportResult(boolean success, String filePath, int recordCount, String message) {
            this.success = success;
            this.filePath = filePath;
            this.recordCount = recordCount;
            this.message = message;
        }
        
        public boolean isSuccess() { return success; }
        public String getFilePath() { return filePath; }
        public int getRecordCount() { return recordCount; }
        public String getMessage() { return message; }
    }
}
