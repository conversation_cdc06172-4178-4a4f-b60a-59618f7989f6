package com.android.packagemanager.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "android_packages")
public class AndroidPackage {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "package_name", nullable = false, unique = true)
    private String packageName;
    
    @Column(name = "app_name")
    private String appName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "package_type", nullable = false)
    private PackageType packageType;
    
    @Column(name = "version_name")
    private String versionName;
    
    @Column(name = "version_code")
    private String versionCode;
    
    @Column(name = "install_time")
    private LocalDateTime installTime;
    
    @Column(name = "update_time")
    private LocalDateTime updateTime;
    
    @Column(name = "is_enabled")
    private Boolean isEnabled;
    
    @Column(name = "apk_path")
    private String apkPath;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    public enum PackageType {
        SYSTEM, USER
    }
    
    // Constructors
    public AndroidPackage() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public AndroidPackage(String packageName, String appName, PackageType packageType) {
        this();
        this.packageName = packageName;
        this.appName = appName;
        this.packageType = packageType;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getPackageName() {
        return packageName;
    }
    
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }
    
    public String getAppName() {
        return appName;
    }
    
    public void setAppName(String appName) {
        this.appName = appName;
    }
    
    public PackageType getPackageType() {
        return packageType;
    }
    
    public void setPackageType(PackageType packageType) {
        this.packageType = packageType;
    }
    
    public String getVersionName() {
        return versionName;
    }
    
    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }
    
    public String getVersionCode() {
        return versionCode;
    }
    
    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }
    
    public LocalDateTime getInstallTime() {
        return installTime;
    }
    
    public void setInstallTime(LocalDateTime installTime) {
        this.installTime = installTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public Boolean getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    public String getApkPath() {
        return apkPath;
    }
    
    public void setApkPath(String apkPath) {
        this.apkPath = apkPath;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
