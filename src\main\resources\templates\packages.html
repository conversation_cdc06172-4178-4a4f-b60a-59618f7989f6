<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::div})}">
<div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-search me-2"></i>包查询
                </div>
                <div class="card-body">
                    <!-- 搜索和过滤 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchKeyword" placeholder="搜索包名或应用名称">
                                <button class="btn btn-outline-secondary" type="button" onclick="searchPackages()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="packageTypeFilter">
                                <option value="all">所有应用</option>
                                <option value="system">系统应用</option>
                                <option value="user">用户应用</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="realtimeSwitch">
                                <label class="form-check-label" for="realtimeSwitch">
                                    实时获取
                                </label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="loadPackages()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center py-2">
                                    <h6 class="card-title mb-1">系统应用</h6>
                                    <h4 class="text-primary mb-0" id="systemCount">-</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center py-2">
                                    <h6 class="card-title mb-1">用户应用</h6>
                                    <h4 class="text-info mb-0" id="userCount">-</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center py-2">
                                    <h6 class="card-title mb-1">总计</h6>
                                    <h4 class="text-success mb-0" id="totalCount">-</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 包列表 -->
                    <div id="packagesContainer">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载包列表...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPackages = [];

        // 加载包列表
        function loadPackages() {
            const packageType = document.getElementById('packageTypeFilter').value;
            const realtime = document.getElementById('realtimeSwitch').checked;
            const keyword = document.getElementById('searchKeyword').value.trim();

            showLoading();

            let url;
            if (packageType === 'all') {
                url = `/api/packages/all?realtime=${realtime}`;
            } else {
                url = `/api/packages/${packageType}?realtime=${realtime}`;
            }

            if (keyword) {
                url += `&keyword=${encodeURIComponent(keyword)}`;
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (packageType === 'all') {
                            currentPackages = [...data.systemPackages, ...data.userPackages];
                            updateCounts(data.systemCount, data.userCount, data.totalCount);
                            displayPackages(currentPackages);
                        } else {
                            currentPackages = data.data;
                            if (packageType === 'system') {
                                updateCounts(data.count, 0, data.count);
                            } else {
                                updateCounts(0, data.count, data.count);
                            }
                            displayPackages(currentPackages);
                        }
                        showToast('包列表加载成功', 'success');
                    } else {
                        showToast('加载包列表失败: ' + data.message, 'error');
                        displayError('加载包列表失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('加载包列表失败:', error);
                    showToast('加载包列表失败', 'error');
                    displayError('网络错误，请检查连接');
                })
                .finally(() => {
                    hideLoading();
                });
        }

        // 搜索包
        function searchPackages() {
            loadPackages();
        }

        // 显示包列表
        function displayPackages(packages) {
            const container = document.getElementById('packagesContainer');
            
            if (packages.length === 0) {
                container.innerHTML = '<div class="text-center text-muted py-4">未找到匹配的应用包</div>';
                return;
            }

            let html = '<div class="table-responsive">';
            html += '<table class="table table-hover">';
            html += '<thead class="table-light">';
            html += '<tr><th>应用名称</th><th>包名</th><th>类型</th><th>操作</th></tr>';
            html += '</thead><tbody>';

            packages.forEach(pkg => {
                html += `
                    <tr>
                        <td>
                            <div class="fw-bold">${pkg.appName || pkg.packageName}</div>
                            ${pkg.versionName ? `<small class="text-muted">版本: ${pkg.versionName}</small>` : ''}
                        </td>
                        <td>
                            <code class="small">${pkg.packageName}</code>
                        </td>
                        <td>${getPackageTypeLabel(pkg.packageType)}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-danger" onclick="uninstallPackage('${pkg.packageName}', '${pkg.appName || pkg.packageName}', '${pkg.packageType}')">
                                <i class="fas fa-trash me-1"></i>卸载
                            </button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // 更新统计数量
        function updateCounts(systemCount, userCount, totalCount) {
            document.getElementById('systemCount').textContent = systemCount;
            document.getElementById('userCount').textContent = userCount;
            document.getElementById('totalCount').textContent = totalCount;
        }

        // 显示错误
        function displayError(message) {
            const container = document.getElementById('packagesContainer');
            container.innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
        }

        // 显示加载状态
        function showLoading() {
            const container = document.getElementById('packagesContainer');
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载包列表...</p>
                </div>
            `;
        }

        function hideLoading() {
            // 加载完成后会调用displayPackages或displayError
        }

        // 卸载包
        function uninstallPackage(packageName, appName, packageType) {
            if (!confirm(`确定要卸载应用 "${appName}" 吗？\n包名: ${packageName}\n类型: ${packageType === 'SYSTEM' ? '系统应用' : '用户应用'}`)) {
                return;
            }

            const operator = prompt('请输入操作人员名称:', 'admin') || 'admin';

            fetch(`/api/uninstall/${encodeURIComponent(packageName)}?operator=${encodeURIComponent(operator)}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`应用 "${appName}" 卸载成功`, 'success');
                    loadPackages(); // 重新加载列表
                } else {
                    showToast(`卸载失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('卸载失败:', error);
                showToast('卸载操作失败', 'error');
            });
        }

        // 搜索框回车事件
        document.getElementById('searchKeyword').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchPackages();
            }
        });

        // 过滤器变化事件
        document.getElementById('packageTypeFilter').addEventListener('change', function() {
            loadPackages();
        });

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            loadPackages();
        });
    </script>
</div>
</html>
