package com.android.packagemanager.controller;

import com.android.packagemanager.service.ExportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/export")
@CrossOrigin(origins = "*")
public class ExportController {
    
    private static final Logger logger = LoggerFactory.getLogger(ExportController.class);
    
    @Autowired
    private ExportService exportService;
    
    /**
     * 导出系统应用列表
     */
    @PostMapping("/system")
    public ResponseEntity<Map<String, Object>> exportSystemPackages(
            @RequestParam(defaultValue = "false") boolean realtime) {
        
        logger.info("导出系统应用列表，实时: {}", realtime);
        
        try {
            ExportService.ExportResult result = exportService.exportSystemPackages(realtime);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("filePath", result.getFilePath());
            response.put("recordCount", result.getRecordCount());
            
            if (result.isSuccess()) {
                response.put("downloadUrl", "/api/export/download?file=" + 
                           URLEncoder.encode(new File(result.getFilePath()).getName(), StandardCharsets.UTF_8));
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("导出系统应用列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "导出失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 导出用户应用列表
     */
    @PostMapping("/user")
    public ResponseEntity<Map<String, Object>> exportUserPackages(
            @RequestParam(defaultValue = "false") boolean realtime) {
        
        logger.info("导出用户应用列表，实时: {}", realtime);
        
        try {
            ExportService.ExportResult result = exportService.exportUserPackages(realtime);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("filePath", result.getFilePath());
            response.put("recordCount", result.getRecordCount());
            
            if (result.isSuccess()) {
                response.put("downloadUrl", "/api/export/download?file=" + 
                           URLEncoder.encode(new File(result.getFilePath()).getName(), StandardCharsets.UTF_8));
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("导出用户应用列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "导出失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 导出所有应用列表
     */
    @PostMapping("/all")
    public ResponseEntity<Map<String, Object>> exportAllPackages(
            @RequestParam(defaultValue = "false") boolean realtime) {
        
        logger.info("导出所有应用列表，实时: {}", realtime);
        
        try {
            ExportService.ExportResult result = exportService.exportAllPackages(realtime);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("filePath", result.getFilePath());
            response.put("recordCount", result.getRecordCount());
            
            if (result.isSuccess()) {
                response.put("downloadUrl", "/api/export/download?file=" + 
                           URLEncoder.encode(new File(result.getFilePath()).getName(), StandardCharsets.UTF_8));
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("导出所有应用列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "导出失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 导出卸载记录
     */
    @PostMapping("/uninstall-records")
    public ResponseEntity<Map<String, Object>> exportUninstallRecords() {
        logger.info("导出卸载记录");
        
        try {
            ExportService.ExportResult result = exportService.exportUninstallRecords();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("filePath", result.getFilePath());
            response.put("recordCount", result.getRecordCount());
            
            if (result.isSuccess()) {
                response.put("downloadUrl", "/api/export/download?file=" + 
                           URLEncoder.encode(new File(result.getFilePath()).getName(), StandardCharsets.UTF_8));
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("导出卸载记录失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "导出失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 下载导出的文件
     */
    @GetMapping("/download")
    public ResponseEntity<Resource> downloadFile(@RequestParam String file) {
        logger.info("下载文件: {}", file);
        
        try {
            // 构建文件路径（简单的安全检查）
            if (file.contains("..") || file.contains("/") || file.contains("\\")) {
                return ResponseEntity.badRequest().build();
            }
            
            File exportFile = new File("exports", file);
            if (!exportFile.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(exportFile);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename*=UTF-8''" + URLEncoder.encode(file, StandardCharsets.UTF_8))
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
            
        } catch (Exception e) {
            logger.error("下载文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
