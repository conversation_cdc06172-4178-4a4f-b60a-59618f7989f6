server:
  port: 8080
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: android-package-manager
  
  datasource:
    url: ******************************
    driver-class-name: org.sqlite.JDBC
    
  jpa:
    database-platform: org.sqlite.hibernate.dialect.SQLiteDialect
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html

logging:
  level:
    com.android.packagemanager: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# ADB配置
adb:
  path: adb
  timeout: 30000
  
# 文件路径配置
file:
  system-app-list: system_app_list.csv
  user-app-list: user_app_list.csv
  export-path: exports/
