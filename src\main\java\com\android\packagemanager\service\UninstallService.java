package com.android.packagemanager.service;

import com.android.packagemanager.entity.AndroidPackage;
import com.android.packagemanager.entity.UninstallRecord;
import com.android.packagemanager.repository.AndroidPackageRepository;
import com.android.packagemanager.repository.UninstallRecordRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class UninstallService {
    
    private static final Logger logger = LoggerFactory.getLogger(UninstallService.class);
    
    @Autowired
    private AdbService adbService;
    
    @Autowired
    private AndroidPackageRepository packageRepository;
    
    @Autowired
    private UninstallRecordRepository uninstallRecordRepository;
    
    /**
     * 卸载单个应用
     */
    public UninstallResult uninstallPackage(String packageName, String operator) {
        logger.info("开始卸载应用: {}, 操作人: {}", packageName, operator);
        
        // 查找应用信息
        Optional<AndroidPackage> packageOpt = packageRepository.findByPackageName(packageName);
        if (!packageOpt.isPresent()) {
            logger.warn("未找到应用: {}", packageName);
            return new UninstallResult(false, packageName, null,
                    AndroidPackage.PackageType.USER, "未找到应用信息", null, 0);
        }
        
        AndroidPackage pkg = packageOpt.get();
        return uninstallPackage(pkg, operator);
    }
    
    /**
     * 卸载应用
     */
    public UninstallResult uninstallPackage(AndroidPackage pkg, String operator) {
        String packageName = pkg.getPackageName();
        String appName = pkg.getAppName();
        AndroidPackage.PackageType packageType = pkg.getPackageType();
        
        logger.info("卸载应用: {} ({}), 类型: {}", packageName, appName, packageType);
        
        long startTime = System.currentTimeMillis();
        AdbService.AdbResult adbResult;
        String command;
        
        // 根据应用类型选择卸载命令
        if (packageType == AndroidPackage.PackageType.SYSTEM) {
            command = String.format("adb shell pm uninstall --user 0 %s", packageName);
            adbResult = adbService.uninstallSystemApp(packageName);
        } else {
            command = String.format("adb uninstall %s", packageName);
            adbResult = adbService.uninstallUserApp(packageName);
        }
        
        long executionTime = System.currentTimeMillis() - startTime;
        
        // 创建卸载记录
        UninstallRecord record = new UninstallRecord(packageName, appName, packageType, 
                adbResult.isSuccess() ? UninstallRecord.UninstallStatus.SUCCESS : UninstallRecord.UninstallStatus.FAILED);
        record.setCommandUsed(command);
        record.setExecutionTime(executionTime);
        record.setOperator(operator);
        
        if (!adbResult.isSuccess()) {
            record.setErrorMessage(adbResult.getErrorMessage());
            logger.error("卸载失败: {} - {}", packageName, adbResult.getErrorMessage());
        } else {
            logger.info("卸载成功: {}", packageName);
        }
        
        // 保存卸载记录
        uninstallRecordRepository.save(record);
        
        return new UninstallResult(adbResult.isSuccess(), packageName, appName, packageType, 
                adbResult.getErrorMessage(), adbResult.getOutput(), executionTime);
    }
    
    /**
     * 批量卸载应用
     */
    public BatchUninstallResult batchUninstall(List<String> packageNames, String operator) {
        logger.info("开始批量卸载 {} 个应用, 操作人: {}", packageNames.size(), operator);
        
        List<UninstallResult> results = new ArrayList<>();
        int successCount = 0;
        int failedCount = 0;
        
        for (String packageName : packageNames) {
            try {
                UninstallResult result = uninstallPackage(packageName, operator);
                results.add(result);
                
                if (result.isSuccess()) {
                    successCount++;
                } else {
                    failedCount++;
                }
                
                // 添加短暂延迟，避免过快执行命令
                Thread.sleep(500);
                
            } catch (Exception e) {
                logger.error("卸载应用 {} 时发生异常", packageName, e);
                
                UninstallResult errorResult = new UninstallResult(false, packageName, null, 
                        AndroidPackage.PackageType.USER, e.getMessage(), null, 0);
                results.add(errorResult);
                failedCount++;
                
                // 记录异常到数据库
                UninstallRecord record = new UninstallRecord(packageName, null, 
                        AndroidPackage.PackageType.USER, UninstallRecord.UninstallStatus.FAILED);
                record.setErrorMessage(e.getMessage());
                record.setOperator(operator);
                uninstallRecordRepository.save(record);
            }
        }
        
        logger.info("批量卸载完成，成功: {}, 失败: {}", successCount, failedCount);
        
        return new BatchUninstallResult(results, successCount, failedCount);
    }
    
    /**
     * 获取卸载记录
     */
    public List<UninstallRecord> getUninstallRecords() {
        return uninstallRecordRepository.findAllOrderByCreatedAtDesc();
    }
    
    /**
     * 获取最近的卸载记录
     */
    public List<UninstallRecord> getRecentUninstallRecords(int hours) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        return uninstallRecordRepository.findRecentRecords(startTime);
    }
    
    /**
     * 根据状态获取卸载记录
     */
    public List<UninstallRecord> getUninstallRecordsByStatus(UninstallRecord.UninstallStatus status) {
        return uninstallRecordRepository.findByStatus(status);
    }
    
    /**
     * 获取卸载统计信息
     */
    public UninstallStatistics getUninstallStatistics() {
        long totalRecords = uninstallRecordRepository.count();
        long successCount = uninstallRecordRepository.countByStatus(UninstallRecord.UninstallStatus.SUCCESS);
        long failedCount = uninstallRecordRepository.countByStatus(UninstallRecord.UninstallStatus.FAILED);
        long cancelledCount = uninstallRecordRepository.countByStatus(UninstallRecord.UninstallStatus.CANCELLED);
        
        return new UninstallStatistics(totalRecords, successCount, failedCount, cancelledCount);
    }
    
    /**
     * 卸载结果
     */
    public static class UninstallResult {
        private final boolean success;
        private final String packageName;
        private final String appName;
        private final AndroidPackage.PackageType packageType;
        private final String errorMessage;
        private final List<String> output;
        private final long executionTime;
        
        public UninstallResult(boolean success, String packageName, String appName, 
                              AndroidPackage.PackageType packageType, String errorMessage, 
                              List<String> output, long executionTime) {
            this.success = success;
            this.packageName = packageName;
            this.appName = appName;
            this.packageType = packageType;
            this.errorMessage = errorMessage;
            this.output = output;
            this.executionTime = executionTime;
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getPackageName() { return packageName; }
        public String getAppName() { return appName; }
        public AndroidPackage.PackageType getPackageType() { return packageType; }
        public String getErrorMessage() { return errorMessage; }
        public List<String> getOutput() { return output; }
        public long getExecutionTime() { return executionTime; }
    }
    
    /**
     * 批量卸载结果
     */
    public static class BatchUninstallResult {
        private final List<UninstallResult> results;
        private final int successCount;
        private final int failedCount;
        
        public BatchUninstallResult(List<UninstallResult> results, int successCount, int failedCount) {
            this.results = results;
            this.successCount = successCount;
            this.failedCount = failedCount;
        }
        
        // Getters
        public List<UninstallResult> getResults() { return results; }
        public int getSuccessCount() { return successCount; }
        public int getFailedCount() { return failedCount; }
        public int getTotalCount() { return results.size(); }
    }
    
    /**
     * 卸载统计信息
     */
    public static class UninstallStatistics {
        private final long totalRecords;
        private final long successCount;
        private final long failedCount;
        private final long cancelledCount;
        
        public UninstallStatistics(long totalRecords, long successCount, long failedCount, long cancelledCount) {
            this.totalRecords = totalRecords;
            this.successCount = successCount;
            this.failedCount = failedCount;
            this.cancelledCount = cancelledCount;
        }
        
        // Getters
        public long getTotalRecords() { return totalRecords; }
        public long getSuccessCount() { return successCount; }
        public long getFailedCount() { return failedCount; }
        public long getCancelledCount() { return cancelledCount; }
        public double getSuccessRate() { 
            return totalRecords > 0 ? (double) successCount / totalRecords * 100 : 0; 
        }
    }
}
