package com.android.packagemanager.controller;

import com.android.packagemanager.entity.UninstallRecord;
import com.android.packagemanager.service.UninstallService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/uninstall")
@CrossOrigin(origins = "*")
public class UninstallController {
    
    private static final Logger logger = LoggerFactory.getLogger(UninstallController.class);
    
    @Autowired
    private UninstallService uninstallService;
    
    /**
     * 卸载单个应用
     */
    @PostMapping("/{packageName}")
    public ResponseEntity<Map<String, Object>> uninstallPackage(
            @PathVariable String packageName,
            @RequestParam(defaultValue = "system") String operator) {
        
        logger.info("收到卸载请求: {}, 操作人: {}", packageName, operator);
        
        try {
            UninstallService.UninstallResult result = uninstallService.uninstallPackage(packageName, operator);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("packageName", result.getPackageName());
            response.put("appName", result.getAppName());
            response.put("packageType", result.getPackageType());
            response.put("executionTime", result.getExecutionTime());
            
            if (result.isSuccess()) {
                response.put("message", "应用卸载成功");
            } else {
                response.put("message", "应用卸载失败");
                response.put("errorMessage", result.getErrorMessage());
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("卸载应用失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "卸载应用失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 批量卸载应用
     */
    @PostMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchUninstall(
            @RequestBody BatchUninstallRequest request) {
        
        logger.info("收到批量卸载请求，应用数量: {}, 操作人: {}", 
                   request.getPackageNames().size(), request.getOperator());
        
        try {
            UninstallService.BatchUninstallResult result = uninstallService.batchUninstall(
                    request.getPackageNames(), request.getOperator());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("totalCount", result.getTotalCount());
            response.put("successCount", result.getSuccessCount());
            response.put("failedCount", result.getFailedCount());
            response.put("results", result.getResults());
            response.put("message", String.format("批量卸载完成，成功: %d, 失败: %d", 
                                                 result.getSuccessCount(), result.getFailedCount()));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("批量卸载失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量卸载失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取卸载记录
     */
    @GetMapping("/records")
    public ResponseEntity<Map<String, Object>> getUninstallRecords(
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "24") int recentHours) {
        
        try {
            List<UninstallRecord> records;
            
            if (status != null && !status.trim().isEmpty()) {
                UninstallRecord.UninstallStatus uninstallStatus = 
                        UninstallRecord.UninstallStatus.valueOf(status.toUpperCase());
                records = uninstallService.getUninstallRecordsByStatus(uninstallStatus);
            } else if (recentHours > 0) {
                records = uninstallService.getRecentUninstallRecords(recentHours);
            } else {
                records = uninstallService.getUninstallRecords();
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", records);
            response.put("count", records.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取卸载记录失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取卸载记录失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取卸载统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getUninstallStatistics() {
        try {
            UninstallService.UninstallStatistics statistics = uninstallService.getUninstallStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取卸载统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取卸载统计信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 批量卸载请求
     */
    public static class BatchUninstallRequest {
        private List<String> packageNames;
        private String operator = "system";
        
        public List<String> getPackageNames() {
            return packageNames;
        }
        
        public void setPackageNames(List<String> packageNames) {
            this.packageNames = packageNames;
        }
        
        public String getOperator() {
            return operator;
        }
        
        public void setOperator(String operator) {
            this.operator = operator;
        }
    }
}
