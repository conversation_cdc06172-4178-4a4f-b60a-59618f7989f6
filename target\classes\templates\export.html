<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::div})}">
<div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-download me-2"></i>数据导出
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 包列表导出 -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <i class="fas fa-mobile-alt me-2"></i>应用包导出
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="realtimeExport">
                                            <label class="form-check-label" for="realtimeExport">
                                                实时获取数据
                                            </label>
                                        </div>
                                        <small class="text-muted">勾选后将从设备实时获取最新数据</small>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-primary" onclick="exportPackages('system')">
                                            <i class="fas fa-download me-1"></i>导出系统应用
                                        </button>
                                        <button class="btn btn-outline-info" onclick="exportPackages('user')">
                                            <i class="fas fa-download me-1"></i>导出用户应用
                                        </button>
                                        <button class="btn btn-primary" onclick="exportPackages('all')">
                                            <i class="fas fa-download me-1"></i>导出所有应用
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 卸载记录导出 -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <i class="fas fa-history me-2"></i>卸载记录导出
                                </div>
                                <div class="card-body">
                                    <p class="text-muted">导出所有卸载操作记录，包括成功和失败的记录。</p>
                                    
                                    <div class="d-grid">
                                        <button class="btn btn-success" onclick="exportUninstallRecords()">
                                            <i class="fas fa-download me-1"></i>导出卸载记录
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 导出历史 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <i class="fas fa-file-csv me-2"></i>导出历史
                                </div>
                                <div class="card-body">
                                    <div id="exportHistory">
                                        <div class="text-center text-muted">
                                            暂无导出记录
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let exportHistory = [];

        // 导出包列表
        function exportPackages(type) {
            const realtime = document.getElementById('realtimeExport').checked;
            
            let url;
            let typeName;
            
            switch(type) {
                case 'system':
                    url = '/api/export/system';
                    typeName = '系统应用';
                    break;
                case 'user':
                    url = '/api/export/user';
                    typeName = '用户应用';
                    break;
                case 'all':
                    url = '/api/export/all';
                    typeName = '所有应用';
                    break;
                default:
                    showToast('无效的导出类型', 'error');
                    return;
            }

            showToast(`正在导出${typeName}列表...`, 'info');

            fetch(`${url}?realtime=${realtime}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`${typeName}列表导出成功，共 ${data.recordCount} 条记录`, 'success');
                    addToExportHistory({
                        type: typeName,
                        fileName: data.filePath.split('/').pop(),
                        recordCount: data.recordCount,
                        downloadUrl: data.downloadUrl,
                        time: new Date()
                    });
                    updateExportHistory();
                } else {
                    showToast(`导出失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('导出失败:', error);
                showToast('导出操作失败', 'error');
            });
        }

        // 导出卸载记录
        function exportUninstallRecords() {
            showToast('正在导出卸载记录...', 'info');

            fetch('/api/export/uninstall-records', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`卸载记录导出成功，共 ${data.recordCount} 条记录`, 'success');
                    addToExportHistory({
                        type: '卸载记录',
                        fileName: data.filePath.split('/').pop(),
                        recordCount: data.recordCount,
                        downloadUrl: data.downloadUrl,
                        time: new Date()
                    });
                    updateExportHistory();
                } else {
                    showToast(`导出失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('导出失败:', error);
                showToast('导出操作失败', 'error');
            });
        }

        // 添加到导出历史
        function addToExportHistory(item) {
            exportHistory.unshift(item);
            // 只保留最近10条记录
            if (exportHistory.length > 10) {
                exportHistory = exportHistory.slice(0, 10);
            }
        }

        // 更新导出历史显示
        function updateExportHistory() {
            const container = document.getElementById('exportHistory');
            
            if (exportHistory.length === 0) {
                container.innerHTML = '<div class="text-center text-muted">暂无导出记录</div>';
                return;
            }

            let html = '<div class="table-responsive">';
            html += '<table class="table table-sm">';
            html += '<thead><tr><th>时间</th><th>类型</th><th>文件名</th><th>记录数</th><th>操作</th></tr></thead>';
            html += '<tbody>';

            exportHistory.forEach(item => {
                html += `
                    <tr>
                        <td class="small">${formatDateTime(item.time)}</td>
                        <td><span class="badge bg-secondary">${item.type}</span></td>
                        <td><code class="small">${item.fileName}</code></td>
                        <td>${item.recordCount}</td>
                        <td>
                            <a href="${item.downloadUrl}" class="btn btn-sm btn-outline-primary" download>
                                <i class="fas fa-download me-1"></i>下载
                            </a>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            updateExportHistory();
        });
    </script>
</div>
</html>
