#!/bin/bash

echo "========================================"
echo "    Android 包管理系统启动脚本"
echo "========================================"
echo

echo "检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "错误：未找到Java环境，请确保已安装Java 17或更高版本"
    exit 1
fi

echo "检查Maven环境..."
if ! command -v mvn &> /dev/null; then
    echo "错误：未找到Maven环境，请确保已安装Maven 3.6或更高版本"
    exit 1
fi

echo "检查ADB环境..."
if ! command -v adb &> /dev/null; then
    echo "警告：未找到ADB工具，请确保ADB已安装并在PATH中"
    echo "或者在application.yml中配置ADB的完整路径"
    echo
fi

echo "检查Android设备连接..."
adb devices
echo

echo "编译项目..."
mvn clean compile -s settings.xml
if [ $? -ne 0 ]; then
    echo "编译失败，请检查错误信息"
    exit 1
fi

echo
echo "启动应用..."
echo "应用启动后请访问: http://localhost:8080"
echo "按 Ctrl+C 可停止应用"
echo

mvn spring-boot:run -s settings.xml
