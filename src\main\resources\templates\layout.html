<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:fragment="layout (content)">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} + ' - Android 包管理系统'">Android 包管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-success {
            color: #28a745;
        }
        .status-failed {
            color: #dc3545;
        }
        .status-cancelled {
            color: #6c757d;
        }
        .package-type-system {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        .package-type-user {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }
        .loading {
            display: none;
        }
        .device-status {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }
        .device-connected {
            background-color: #d4edda;
            color: #155724;
        }
        .device-disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-mobile-alt me-2"></i>
                Android 包管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/packages">
                            <i class="fas fa-search me-1"></i>包查询
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/uninstall">
                            <i class="fas fa-trash me-1"></i>包卸载
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/records">
                            <i class="fas fa-history me-1"></i>卸载记录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/export">
                            <i class="fas fa-download me-1"></i>数据导出
                        </a>
                    </li>
                </ul>
                
                <!-- 设备状态 -->
                <div class="d-flex align-items-center">
                    <span id="deviceStatus" class="device-status device-disconnected">
                        <i class="fas fa-circle me-1"></i>设备未连接
                    </span>
                    <button class="btn btn-outline-light btn-sm ms-2" onclick="checkDeviceStatus()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <div th:replace="${content}"></div>
    </div>

    <!-- Toast 容器 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">系统消息</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastBody">
                消息内容
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // 显示Toast消息
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            const toastBody = document.getElementById('toastBody');
            const toastHeader = toast.querySelector('.toast-header i');
            
            toastBody.textContent = message;
            
            // 设置图标和颜色
            toastHeader.className = `fas me-2 ${getToastIconClass(type)}`;
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }
        
        function getToastIconClass(type) {
            switch(type) {
                case 'success': return 'fa-check-circle text-success';
                case 'error': return 'fa-exclamation-circle text-danger';
                case 'warning': return 'fa-exclamation-triangle text-warning';
                default: return 'fa-info-circle text-primary';
            }
        }
        
        // 检查设备状态
        function checkDeviceStatus() {
            fetch('/api/packages/device/status')
                .then(response => response.json())
                .then(data => {
                    const statusElement = document.getElementById('deviceStatus');
                    if (data.connected) {
                        statusElement.className = 'device-status device-connected';
                        statusElement.innerHTML = '<i class="fas fa-circle me-1"></i>设备已连接';
                    } else {
                        statusElement.className = 'device-status device-disconnected';
                        statusElement.innerHTML = '<i class="fas fa-circle me-1"></i>设备未连接';
                    }
                })
                .catch(error => {
                    console.error('检查设备状态失败:', error);
                    showToast('检查设备状态失败', 'error');
                });
        }
        
        // 页面加载时检查设备状态
        document.addEventListener('DOMContentLoaded', function() {
            checkDeviceStatus();
            // 每30秒自动检查一次设备状态
            setInterval(checkDeviceStatus, 30000);
        });
        
        // 显示/隐藏加载状态
        function showLoading(element) {
            if (element) {
                element.style.display = 'inline-block';
            }
        }
        
        function hideLoading(element) {
            if (element) {
                element.style.display = 'none';
            }
        }
        
        // 格式化日期时间
        function formatDateTime(dateTimeString) {
            if (!dateTimeString) return '';
            const date = new Date(dateTimeString);
            return date.toLocaleString('zh-CN');
        }
        
        // 获取包类型标签
        function getPackageTypeLabel(type) {
            return type === 'SYSTEM' ? 
                '<span class="badge package-type-system">系统应用</span>' :
                '<span class="badge package-type-user">用户应用</span>';
        }
        
        // 获取状态标签
        function getStatusLabel(status) {
            switch(status) {
                case 'SUCCESS':
                    return '<span class="badge bg-success">成功</span>';
                case 'FAILED':
                    return '<span class="badge bg-danger">失败</span>';
                case 'CANCELLED':
                    return '<span class="badge bg-secondary">取消</span>';
                default:
                    return '<span class="badge bg-secondary">' + status + '</span>';
            }
        }
    </script>
</body>
</html>
