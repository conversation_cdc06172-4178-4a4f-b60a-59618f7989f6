<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::div})}">
<div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-history me-2"></i>卸载记录
                </div>
                <div class="card-body">
                    <!-- 过滤器 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">所有状态</option>
                                <option value="SUCCESS">成功</option>
                                <option value="FAILED">失败</option>
                                <option value="CANCELLED">取消</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="timeFilter">
                                <option value="24">最近24小时</option>
                                <option value="72">最近3天</option>
                                <option value="168">最近7天</option>
                                <option value="720">最近30天</option>
                                <option value="0">所有记录</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="searchKeyword" placeholder="搜索包名或应用名称">
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="loadRecords()">
                                <i class="fas fa-search me-1"></i>查询
                            </button>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="row mb-3" id="statisticsRow">
                        <div class="col-md-3 text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>

                    <!-- 记录列表 -->
                    <div id="recordsContainer">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载卸载记录...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentRecords = [];

        // 加载记录
        function loadRecords() {
            const status = document.getElementById('statusFilter').value;
            const timeFilter = document.getElementById('timeFilter').value;
            const keyword = document.getElementById('searchKeyword').value.trim();

            showLoading();

            let url = '/api/uninstall/records?';
            if (status) {
                url += `status=${status}&`;
            }
            if (timeFilter && timeFilter !== '0') {
                url += `recentHours=${timeFilter}&`;
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentRecords = data.data;
                        
                        // 如果有搜索关键词，进行客户端过滤
                        if (keyword) {
                            currentRecords = currentRecords.filter(record => 
                                (record.packageName && record.packageName.toLowerCase().includes(keyword.toLowerCase())) ||
                                (record.appName && record.appName.toLowerCase().includes(keyword.toLowerCase()))
                            );
                        }
                        
                        displayRecords(currentRecords);
                        showToast('记录加载成功', 'success');
                    } else {
                        showToast('加载记录失败: ' + data.message, 'error');
                        displayError('加载记录失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('加载记录失败:', error);
                    showToast('加载记录失败', 'error');
                    displayError('网络错误，请检查连接');
                })
                .finally(() => {
                    hideLoading();
                });
        }

        // 显示记录
        function displayRecords(records) {
            const container = document.getElementById('recordsContainer');
            
            if (records.length === 0) {
                container.innerHTML = '<div class="text-center text-muted py-4">未找到匹配的记录</div>';
                return;
            }

            let html = '<div class="table-responsive">';
            html += '<table class="table table-hover">';
            html += '<thead class="table-light">';
            html += '<tr><th>时间</th><th>应用</th><th>类型</th><th>状态</th><th>执行时间</th><th>操作人</th><th>错误信息</th></tr>';
            html += '</thead><tbody>';

            records.forEach(record => {
                html += `
                    <tr>
                        <td class="small">${formatDateTime(record.createdAt)}</td>
                        <td>
                            <div class="fw-bold">${record.appName || record.packageName}</div>
                            <code class="small text-muted">${record.packageName}</code>
                        </td>
                        <td>${getPackageTypeLabel(record.packageType)}</td>
                        <td>${getStatusLabel(record.status)}</td>
                        <td class="small">${record.executionTime || 0}ms</td>
                        <td class="small">${record.operator || '-'}</td>
                        <td class="small text-danger">${record.errorMessage || '-'}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // 显示错误
        function displayError(message) {
            const container = document.getElementById('recordsContainer');
            container.innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
        }

        // 显示加载状态
        function showLoading() {
            const container = document.getElementById('recordsContainer');
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载记录...</p>
                </div>
            `;
        }

        function hideLoading() {
            // 加载完成后会调用displayRecords或displayError
        }

        // 加载统计信息
        function loadStatistics() {
            fetch('/api/uninstall/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.data;
                        const html = `
                            <div class="col-md-3 text-center">
                                <div class="card bg-light">
                                    <div class="card-body py-2">
                                        <h6 class="card-title mb-1">总操作数</h6>
                                        <h4 class="text-primary mb-0">${stats.totalRecords}</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card bg-light">
                                    <div class="card-body py-2">
                                        <h6 class="card-title mb-1">成功</h6>
                                        <h4 class="text-success mb-0">${stats.successCount}</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card bg-light">
                                    <div class="card-body py-2">
                                        <h6 class="card-title mb-1">失败</h6>
                                        <h4 class="text-danger mb-0">${stats.failedCount}</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card bg-light">
                                    <div class="card-body py-2">
                                        <h6 class="card-title mb-1">成功率</h6>
                                        <h4 class="text-info mb-0">${stats.successRate.toFixed(1)}%</h4>
                                    </div>
                                </div>
                            </div>
                        `;
                        document.getElementById('statisticsRow').innerHTML = html;
                    }
                })
                .catch(error => {
                    console.error('加载统计信息失败:', error);
                });
        }

        // 搜索框回车事件
        document.getElementById('searchKeyword').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadRecords();
            }
        });

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadRecords();
        });
    </script>
</div>
</html>
