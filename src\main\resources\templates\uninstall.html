<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::div})}">
<div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-trash me-2"></i>包卸载
                </div>
                <div class="card-body">
                    <!-- 批量卸载 -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <label for="packageList" class="form-label">包名列表（每行一个）</label>
                            <textarea class="form-control" id="packageList" rows="6" placeholder="请输入要卸载的包名，每行一个&#10;例如：&#10;com.example.app1&#10;com.example.app2"></textarea>
                        </div>
                        <div class="col-md-4">
                            <label for="operatorName" class="form-label">操作人员</label>
                            <input type="text" class="form-control" id="operatorName" value="admin" placeholder="请输入操作人员名称">
                            
                            <div class="mt-3">
                                <button class="btn btn-danger w-100" onclick="batchUninstall()">
                                    <i class="fas fa-trash me-1"></i>批量卸载
                                </button>
                            </div>
                            
                            <div class="mt-2">
                                <button class="btn btn-outline-secondary w-100" onclick="clearPackageList()">
                                    <i class="fas fa-eraser me-1"></i>清空列表
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 卸载结果 -->
                    <div id="uninstallResults" style="display: none;">
                        <h5>卸载结果</h5>
                        <div id="resultsContainer"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 批量卸载
        function batchUninstall() {
            const packageListText = document.getElementById('packageList').value.trim();
            const operator = document.getElementById('operatorName').value.trim() || 'admin';

            if (!packageListText) {
                showToast('请输入要卸载的包名', 'warning');
                return;
            }

            const packageNames = packageListText.split('\n')
                .map(line => line.trim())
                .filter(line => line.length > 0);

            if (packageNames.length === 0) {
                showToast('请输入有效的包名', 'warning');
                return;
            }

            if (!confirm(`确定要卸载 ${packageNames.length} 个应用吗？\n操作人员: ${operator}`)) {
                return;
            }

            // 显示进度
            showUninstallProgress(packageNames.length);

            const requestData = {
                packageNames: packageNames,
                operator: operator
            };

            fetch('/api/uninstall/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`批量卸载完成，成功: ${data.successCount}, 失败: ${data.failedCount}`, 'info');
                    displayUninstallResults(data.results);
                } else {
                    showToast('批量卸载失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('批量卸载失败:', error);
                showToast('批量卸载操作失败', 'error');
            })
            .finally(() => {
                hideUninstallProgress();
            });
        }

        // 显示卸载进度
        function showUninstallProgress(totalCount) {
            const resultsDiv = document.getElementById('uninstallResults');
            const container = document.getElementById('resultsContainer');
            
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">卸载中...</span>
                    </div>
                    <p class="mt-2">正在卸载 ${totalCount} 个应用，请稍候...</p>
                </div>
            `;
            
            resultsDiv.style.display = 'block';
        }

        // 隐藏卸载进度
        function hideUninstallProgress() {
            // 结果会在displayUninstallResults中显示
        }

        // 显示卸载结果
        function displayUninstallResults(results) {
            const container = document.getElementById('resultsContainer');
            
            let html = '<div class="table-responsive">';
            html += '<table class="table table-sm">';
            html += '<thead><tr><th>包名</th><th>应用名称</th><th>类型</th><th>状态</th><th>执行时间</th><th>错误信息</th></tr></thead>';
            html += '<tbody>';

            results.forEach(result => {
                const statusClass = result.success ? 'text-success' : 'text-danger';
                const statusIcon = result.success ? 'fa-check-circle' : 'fa-times-circle';
                const statusText = result.success ? '成功' : '失败';

                html += `
                    <tr>
                        <td><code class="small">${result.packageName}</code></td>
                        <td>${result.appName || '-'}</td>
                        <td>${getPackageTypeLabel(result.packageType)}</td>
                        <td class="${statusClass}">
                            <i class="fas ${statusIcon} me-1"></i>${statusText}
                        </td>
                        <td>${result.executionTime}ms</td>
                        <td class="text-danger small">${result.errorMessage || '-'}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';

            // 添加统计信息
            const successCount = results.filter(r => r.success).length;
            const failedCount = results.filter(r => !r.success).length;
            
            html += `
                <div class="row mt-3">
                    <div class="col-md-4 text-center">
                        <div class="card bg-light">
                            <div class="card-body py-2">
                                <h6 class="text-success mb-1">成功</h6>
                                <h4 class="text-success mb-0">${successCount}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="card bg-light">
                            <div class="card-body py-2">
                                <h6 class="text-danger mb-1">失败</h6>
                                <h4 class="text-danger mb-0">${failedCount}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="card bg-light">
                            <div class="card-body py-2">
                                <h6 class="text-info mb-1">总计</h6>
                                <h4 class="text-info mb-0">${results.length}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // 清空包列表
        function clearPackageList() {
            document.getElementById('packageList').value = '';
            document.getElementById('uninstallResults').style.display = 'none';
        }
    </script>
</div>
</html>
