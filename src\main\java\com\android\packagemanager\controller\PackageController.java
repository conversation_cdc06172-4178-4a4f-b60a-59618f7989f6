package com.android.packagemanager.controller;

import com.android.packagemanager.entity.AndroidPackage;
import com.android.packagemanager.service.PackageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/packages")
@CrossOrigin(origins = "*")
public class PackageController {
    
    private static final Logger logger = LoggerFactory.getLogger(PackageController.class);
    
    @Autowired
    private PackageService packageService;
    
    /**
     * 获取系统应用列表
     */
    @GetMapping("/system")
    public ResponseEntity<Map<String, Object>> getSystemPackages(
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "false") boolean realtime) {
        
        logger.info("获取系统应用列表，关键词: {}, 实时: {}", keyword, realtime);
        
        try {
            List<AndroidPackage> packages;
            
            if (realtime) {
                // 实时从ADB获取
                packages = packageService.getSystemPackagesFromAdb();
                if (keyword != null && !keyword.trim().isEmpty()) {
                    packages = packages.stream()
                            .filter(pkg -> pkg.getPackageName().contains(keyword) || 
                                         (pkg.getAppName() != null && pkg.getAppName().contains(keyword)))
                            .toList();
                }
            } else {
                // 从数据库获取
                packages = packageService.searchPackages(AndroidPackage.PackageType.SYSTEM, keyword);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", packages);
            response.put("count", packages.size());
            response.put("type", "SYSTEM");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取系统应用列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取系统应用列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取用户应用列表
     */
    @GetMapping("/user")
    public ResponseEntity<Map<String, Object>> getUserPackages(
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "false") boolean realtime) {
        
        logger.info("获取用户应用列表，关键词: {}, 实时: {}", keyword, realtime);
        
        try {
            List<AndroidPackage> packages;
            
            if (realtime) {
                // 实时从ADB获取
                packages = packageService.getUserPackagesFromAdb();
                if (keyword != null && !keyword.trim().isEmpty()) {
                    packages = packages.stream()
                            .filter(pkg -> pkg.getPackageName().contains(keyword) || 
                                         (pkg.getAppName() != null && pkg.getAppName().contains(keyword)))
                            .toList();
                }
            } else {
                // 从数据库获取
                packages = packageService.searchPackages(AndroidPackage.PackageType.USER, keyword);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", packages);
            response.put("count", packages.size());
            response.put("type", "USER");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取用户应用列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取用户应用列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取所有应用列表
     */
    @GetMapping("/all")
    public ResponseEntity<Map<String, Object>> getAllPackages(
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "false") boolean realtime) {
        
        logger.info("获取所有应用列表，关键词: {}, 实时: {}", keyword, realtime);
        
        try {
            Map<String, Object> response = new HashMap<>();
            
            List<AndroidPackage> systemPackages;
            List<AndroidPackage> userPackages;
            
            if (realtime) {
                systemPackages = packageService.getSystemPackagesFromAdb();
                userPackages = packageService.getUserPackagesFromAdb();
                
                if (keyword != null && !keyword.trim().isEmpty()) {
                    systemPackages = systemPackages.stream()
                            .filter(pkg -> pkg.getPackageName().contains(keyword) || 
                                         (pkg.getAppName() != null && pkg.getAppName().contains(keyword)))
                            .toList();
                    userPackages = userPackages.stream()
                            .filter(pkg -> pkg.getPackageName().contains(keyword) || 
                                         (pkg.getAppName() != null && pkg.getAppName().contains(keyword)))
                            .toList();
                }
            } else {
                systemPackages = packageService.searchPackages(AndroidPackage.PackageType.SYSTEM, keyword);
                userPackages = packageService.searchPackages(AndroidPackage.PackageType.USER, keyword);
            }
            
            response.put("success", true);
            response.put("systemPackages", systemPackages);
            response.put("userPackages", userPackages);
            response.put("systemCount", systemPackages.size());
            response.put("userCount", userPackages.size());
            response.put("totalCount", systemPackages.size() + userPackages.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取所有应用列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取所有应用列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 根据包名获取应用详情
     */
    @GetMapping("/{packageName}")
    public ResponseEntity<Map<String, Object>> getPackageDetail(@PathVariable String packageName) {
        logger.info("获取应用详情: {}", packageName);
        
        try {
            Optional<AndroidPackage> packageOpt = packageService.getPackageByName(packageName);
            
            Map<String, Object> response = new HashMap<>();
            if (packageOpt.isPresent()) {
                response.put("success", true);
                response.put("data", packageOpt.get());
            } else {
                response.put("success", false);
                response.put("message", "未找到应用: " + packageName);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取应用详情失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取应用详情失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 同步包信息到数据库
     */
    @PostMapping("/sync")
    public ResponseEntity<Map<String, Object>> syncPackages() {
        logger.info("开始同步包信息");
        
        try {
            packageService.syncPackagesToDatabase();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "包信息同步完成");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("同步包信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "同步包信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 检查设备连接状态
     */
    @GetMapping("/device/status")
    public ResponseEntity<Map<String, Object>> getDeviceStatus() {
        try {
            boolean connected = packageService.isDeviceConnected();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("connected", connected);
            response.put("message", connected ? "设备已连接" : "设备未连接");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("检查设备状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("connected", false);
            response.put("message", "检查设备状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
