package com.android.packagemanager.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "uninstall_records")
public class UninstallRecord {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "package_name", nullable = false)
    private String packageName;
    
    @Column(name = "app_name")
    private String appName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "package_type", nullable = false)
    private AndroidPackage.PackageType packageType;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private UninstallStatus status;
    
    @Column(name = "command_used", length = 500)
    private String commandUsed;
    
    @Column(name = "error_message", length = 1000)
    private String errorMessage;
    
    @Column(name = "execution_time")
    private Long executionTime; // 执行时间（毫秒）
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "operator")
    private String operator; // 操作人员
    
    public enum UninstallStatus {
        SUCCESS, FAILED, CANCELLED
    }
    
    // Constructors
    public UninstallRecord() {
        this.createdAt = LocalDateTime.now();
    }
    
    public UninstallRecord(String packageName, String appName, 
                          AndroidPackage.PackageType packageType, 
                          UninstallStatus status) {
        this();
        this.packageName = packageName;
        this.appName = appName;
        this.packageType = packageType;
        this.status = status;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getPackageName() {
        return packageName;
    }
    
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }
    
    public String getAppName() {
        return appName;
    }
    
    public void setAppName(String appName) {
        this.appName = appName;
    }
    
    public AndroidPackage.PackageType getPackageType() {
        return packageType;
    }
    
    public void setPackageType(AndroidPackage.PackageType packageType) {
        this.packageType = packageType;
    }
    
    public UninstallStatus getStatus() {
        return status;
    }
    
    public void setStatus(UninstallStatus status) {
        this.status = status;
    }
    
    public String getCommandUsed() {
        return commandUsed;
    }
    
    public void setCommandUsed(String commandUsed) {
        this.commandUsed = commandUsed;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public Long getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
}
