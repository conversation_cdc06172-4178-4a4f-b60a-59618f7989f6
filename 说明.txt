获取包列表

	adb shell pm list packages>D:\app\download\K70Pro_dsu\mi\k70\x\all.txt

	环境变量新增
	D:\mysoft\Android\Sdk\build-tools\36.0.0



	.\get_app_names.ps1

	$platformTools = "D:\mysoft\Android\Sdk\build-tools\36.0.0"



卸载
	# 使用默认文件
	.\uninstall_apps.ps1

	# 指定自定义文件
	.\uninstall_apps.ps1 -inputCsv "C:\custom_apps.csv"
	
	.\uninstall_apps.ps1 -inputCsv system_app_list.csv


	系统应用用此命令
	adb shell pm uninstall --user 0 com.android.browser
	
	用户应用用此命令
	adb uninstall com.android.browser

	#有的不能卸载导致报错。你把报错的应用前面用#注释掉（输入文件和结果文件），然后询问用户是否继续执行下一个应用的卸载。注意不要把日志文件记录到结果文件里面了
	
	