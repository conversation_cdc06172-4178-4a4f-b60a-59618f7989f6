# Android 包管理系统

一个基于Spring Boot的Android应用包管理系统，提供包查询、卸载和导出功能。

## 功能特性

### 1. 包查询
- 支持查询系统应用和用户应用
- 区分显示系统软件和用户软件
- 支持实时从ADB获取最新数据
- 支持关键词搜索（包名和应用名称）
- 显示应用名称、包名、类型等信息

### 2. 包卸载
- **系统应用卸载**：使用命令 `adb shell pm uninstall --user 0 <package_name>`
- **用户应用卸载**：使用命令 `adb uninstall <package_name>`
- 支持单个应用卸载和批量卸载
- 记录所有卸载操作（成功和失败）

### 3. 卸载记录管理
- 记录所有卸载操作的详细信息
- 包括操作时间、应用信息、执行结果、错误信息等
- 支持按状态、时间范围筛选记录
- 提供统计信息（总操作数、成功率等）

### 4. 数据导出
- 导出系统应用列表到CSV文件
- 导出用户应用列表到CSV文件
- 导出所有应用列表到CSV文件
- 导出卸载记录到CSV文件
- 支持实时数据导出和数据库数据导出

## 技术栈

- **后端框架**：Spring Boot 3.2.0
- **数据库**：SQLite（使用JPA/Hibernate）
- **前端**：Thymeleaf + Bootstrap 5 + jQuery
- **CSV处理**：OpenCSV
- **ADB集成**：通过ProcessBuilder执行ADB命令

## 项目结构

```
src/
├── main/
│   ├── java/com/android/packagemanager/
│   │   ├── controller/          # 控制器层
│   │   │   ├── WebController.java
│   │   │   ├── PackageController.java
│   │   │   ├── UninstallController.java
│   │   │   └── ExportController.java
│   │   ├── service/             # 服务层
│   │   │   ├── AdbService.java
│   │   │   ├── PackageService.java
│   │   │   ├── UninstallService.java
│   │   │   └── ExportService.java
│   │   ├── entity/              # 实体类
│   │   │   ├── AndroidPackage.java
│   │   │   └── UninstallRecord.java
│   │   ├── repository/          # 数据访问层
│   │   │   ├── AndroidPackageRepository.java
│   │   │   └── UninstallRecordRepository.java
│   │   └── AndroidPackageManagerApplication.java
│   └── resources/
│       ├── templates/           # Thymeleaf模板
│       │   ├── layout.html
│       │   ├── index.html
│       │   ├── packages.html
│       │   ├── uninstall.html
│       │   ├── records.html
│       │   └── export.html
│       └── application.yml      # 配置文件
```

## 安装和运行

### 前置要求

1. **Java 17+**
2. **Maven 3.6+**
3. **ADB工具**：确保ADB已安装并在PATH中
4. **Android设备**：通过USB连接并开启USB调试

### 运行步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd android-package-manager
   ```

2. **配置ADB路径**（可选）
   编辑 `src/main/resources/application.yml`：
   ```yaml
   adb:
     path: adb  # 如果ADB不在PATH中，请指定完整路径
     timeout: 30000
   ```

3. **编译和运行**
   ```bash
   mvn clean install
   mvn spring-boot:run
   ```

4. **访问应用**
   打开浏览器访问：http://localhost:8080

### Docker运行（可选）

```bash
# 构建镜像
docker build -t android-package-manager .

# 运行容器
docker run -p 8080:8080 -v /path/to/exports:/app/exports android-package-manager
```

## 使用说明

### 1. 设备连接检查
- 系统会自动检查Android设备连接状态
- 导航栏右侧显示设备连接状态
- 点击刷新按钮可手动检查连接状态

### 2. 包查询
- 访问"包查询"页面
- 选择应用类型（系统应用/用户应用/所有应用）
- 可选择"实时获取"从设备获取最新数据
- 支持关键词搜索

### 3. 包卸载
- 访问"包卸载"页面
- 在文本框中输入要卸载的包名（每行一个）
- 输入操作人员名称
- 点击"批量卸载"执行操作

### 4. 查看记录
- 访问"卸载记录"页面
- 可按状态、时间范围筛选记录
- 查看详细的操作结果和错误信息

### 5. 数据导出
- 访问"数据导出"页面
- 选择要导出的数据类型
- 可选择实时获取数据
- 导出的CSV文件支持Excel打开

## API接口

### 包查询接口
- `GET /api/packages/system` - 获取系统应用列表
- `GET /api/packages/user` - 获取用户应用列表
- `GET /api/packages/all` - 获取所有应用列表
- `GET /api/packages/{packageName}` - 获取应用详情
- `GET /api/packages/device/status` - 检查设备状态

### 卸载接口
- `POST /api/uninstall/{packageName}` - 卸载单个应用
- `POST /api/uninstall/batch` - 批量卸载应用
- `GET /api/uninstall/records` - 获取卸载记录
- `GET /api/uninstall/statistics` - 获取卸载统计

### 导出接口
- `POST /api/export/system` - 导出系统应用
- `POST /api/export/user` - 导出用户应用
- `POST /api/export/all` - 导出所有应用
- `POST /api/export/uninstall-records` - 导出卸载记录
- `GET /api/export/download` - 下载导出文件

## 配置说明

### 应用配置 (application.yml)

```yaml
server:
  port: 8080

spring:
  datasource:
    url: ******************************
  jpa:
    hibernate:
      ddl-auto: update

adb:
  path: adb
  timeout: 30000

file:
  system-app-list: system_app_list.csv
  user-app-list: user_app_list.csv
  export-path: exports/
```

## 注意事项

1. **设备权限**：确保Android设备已开启USB调试并授权计算机
2. **ADB版本**：建议使用最新版本的ADB工具
3. **系统应用卸载**：卸载系统应用可能影响设备正常使用，请谨慎操作
4. **数据备份**：重要应用卸载前请做好数据备份
5. **网络安全**：本系统无授权验证，请在安全环境中使用

## 故障排除

### 常见问题

1. **设备未连接**
   - 检查USB连接
   - 确认USB调试已开启
   - 运行 `adb devices` 检查设备状态

2. **ADB命令失败**
   - 检查ADB路径配置
   - 确认ADB版本兼容性
   - 重启ADB服务：`adb kill-server && adb start-server`

3. **应用卸载失败**
   - 某些系统应用无法卸载
   - 检查应用是否正在运行
   - 确认设备root权限（如需要）

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
