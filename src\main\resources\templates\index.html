<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::div})}">
<div>
    <div class="row">
        <div class="col-12">
            <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
                <h1 class="display-4">
                    <i class="fas fa-mobile-alt me-3"></i>
                    Android 包管理系统
                </h1>
                <p class="lead">统一管理Android设备上的应用包，支持查询、卸载和导出功能</p>
                <hr class="my-4" style="border-color: rgba(255,255,255,0.3);">
                <p>通过ADB命令与Android设备通信，提供便捷的包管理界面</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 功能卡片 -->
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-search fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">包查询</h5>
                    <p class="card-text">查询系统应用和用户应用，支持实时获取和关键词搜索</p>
                    <a href="/packages" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-1"></i>开始查询
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-trash fa-3x text-danger"></i>
                    </div>
                    <h5 class="card-title">包卸载</h5>
                    <p class="card-text">卸载系统应用和用户应用，支持单个和批量操作</p>
                    <a href="/uninstall" class="btn btn-danger">
                        <i class="fas fa-arrow-right me-1"></i>开始卸载
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-history fa-3x text-info"></i>
                    </div>
                    <h5 class="card-title">卸载记录</h5>
                    <p class="card-text">查看历史卸载记录，包括成功和失败的操作</p>
                    <a href="/records" class="btn btn-info">
                        <i class="fas fa-arrow-right me-1"></i>查看记录
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-download fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">数据导出</h5>
                    <p class="card-text">导出包列表和卸载记录到CSV文件</p>
                    <a href="/export" class="btn btn-success">
                        <i class="fas fa-arrow-right me-1"></i>开始导出
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-2"></i>系统统计
                </div>
                <div class="card-body">
                    <div class="row" id="statisticsRow">
                        <div class="col-md-3 text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近操作 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-clock me-2"></i>最近操作
                </div>
                <div class="card-body">
                    <div id="recentOperations">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 加载统计信息
        function loadStatistics() {
            fetch('/api/uninstall/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.data;
                        const html = `
                            <div class="col-md-3 text-center">
                                <h4 class="text-primary">${stats.totalRecords}</h4>
                                <p class="mb-0">总操作数</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-success">${stats.successCount}</h4>
                                <p class="mb-0">成功操作</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-danger">${stats.failedCount}</h4>
                                <p class="mb-0">失败操作</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-info">${stats.successRate.toFixed(1)}%</h4>
                                <p class="mb-0">成功率</p>
                            </div>
                        `;
                        document.getElementById('statisticsRow').innerHTML = html;
                    } else {
                        document.getElementById('statisticsRow').innerHTML = 
                            '<div class="col-12 text-center text-muted">暂无统计数据</div>';
                    }
                })
                .catch(error => {
                    console.error('加载统计信息失败:', error);
                    document.getElementById('statisticsRow').innerHTML = 
                        '<div class="col-12 text-center text-danger">加载统计信息失败</div>';
                });
        }

        // 加载最近操作
        function loadRecentOperations() {
            fetch('/api/uninstall/records?recentHours=24')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data.length > 0) {
                        const records = data.data.slice(0, 5); // 只显示最近5条
                        let html = '<div class="table-responsive"><table class="table table-sm">';
                        html += '<thead><tr><th>时间</th><th>应用</th><th>类型</th><th>状态</th><th>操作人</th></tr></thead><tbody>';
                        
                        records.forEach(record => {
                            html += `
                                <tr>
                                    <td>${formatDateTime(record.createdAt)}</td>
                                    <td>
                                        <div class="fw-bold">${record.appName || record.packageName}</div>
                                        <small class="text-muted">${record.packageName}</small>
                                    </td>
                                    <td>${getPackageTypeLabel(record.packageType)}</td>
                                    <td>${getStatusLabel(record.status)}</td>
                                    <td>${record.operator || '-'}</td>
                                </tr>
                            `;
                        });
                        
                        html += '</tbody></table></div>';
                        html += '<div class="text-center mt-2"><a href="/records" class="btn btn-outline-primary btn-sm">查看全部记录</a></div>';
                        
                        document.getElementById('recentOperations').innerHTML = html;
                    } else {
                        document.getElementById('recentOperations').innerHTML = 
                            '<div class="text-center text-muted">暂无最近操作记录</div>';
                    }
                })
                .catch(error => {
                    console.error('加载最近操作失败:', error);
                    document.getElementById('recentOperations').innerHTML = 
                        '<div class="text-center text-danger">加载最近操作失败</div>';
                });
        }

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadRecentOperations();
        });
    </script>
</div>
</html>
